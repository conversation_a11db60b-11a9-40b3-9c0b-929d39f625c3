<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>He<PERSON>ett<PERSON>Packard</title>
  <link rel="stylesheet" href="style.css">
  <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
  <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;700&display=swap" rel="stylesheet">
</head>

<body>

  <!-- Main Container -->
  <div id="main">
    <div class="container" id="Home">
      <video class="back-videos" autoplay loop muted plays-inline src="videos/galaxy.mp4" type="video/mp4"></video>

      <!-- Header -->
      <header>
        <div class="left">
          <img src="images/MyLogo.jpg" alt="logo">
          <h1><span style="color: aqua;">Designing </span>Tomorrow.</h1>
        </div>
         <!-- Navigation Links -->
        <ul>
          <li><a href="#Home">Home</a></li>
          <li><a href="#about">About</a></li>
          <li><a href="#downloads">Downloads</a></li>
          <li><a href="#contact">Contact</a></li>
        </ul>

         <!-- Box Icons -->
        <div class="box-icon">
          <a href="https://github.com/shimulhp/main-branch"><i class='bx bxl-github'></i></a>
          <a href="https://git-scm.com/"><i class='bx bxl-git'></i></a>
          <a href="https://code.visualstudio.com/"><i class='bx bxl-visual-studio'></i></a>
        </div>

        <!-- Menu Icon -->
        <div class="menu-icon"><i class='bx bx-menu'></i></div>
      </header>

      <!-- Sidebar -->
      <div class="sidebar">
        <div class="close-icon">
          <i class='bx bx-x'></i>
        </div>

        <!-- Sidebar Header -->
        <div class="sidebar-header">
          <div class="sidebar-logo">
            <img src="images/MyLogo.jpg" alt="logo">
          </div>
          <h2>Navigation</h2>
          <p>Explore my portfolio</p>
        </div>

        <ul>
          <li><a href="#Home">Home</a></li>
          <li><a href="#about">About</a></li>
          <li><a href="#downloads">Downloads</a></li>
          <li><a href="#contact">Contact</a></li>
        </ul>

        <div class="social-icon">
          <a href="https://github.com/shimulhp/main-branch" title="GitHub"><i class='bx bxl-github'></i></a>
          <a href="https://git-scm.com" title="Git"><i class='bx bxl-git'></i></a>
          <a href="https://code.visualstudio.com/" title="VS Code"><i class='bx bxl-visual-studio'></i></a>
        </div>
      </div>

      <!-- Blackhole Background -->
      <div class="blackhole-box">
        <video autoplay loop muted Plays-inline src="videos/blackhole.mp4" type="video/mp4"></video>
        <div class="hover-sign"></div>
      </div>

      <!-- Hero Section -->
      <section class="hero" id="hero">
        <div class="hero-info autoBlur">
          <div class="hero-info-title">
            <i class='bx bxl-sketch'></i>
            Frontend Developer Portfolio
          </div>
          <h1>
            <span class="gradient">Where</span>
            <span class="gradient">Creativity</span>
            <span class="gradient">Meets</span>
            <span class="gradient">Technology</span>
          </h1>

          <p>I’m a developer and designer who loves turning complex problems into simple, beautiful solutions.
            Let’s build something awesome together.</p>

          <button onclick="scrollToContact()" class="hero-cta-btn">
            <i class='bx bx-send'></i>
            <span>Contact Me</span>
          </button>
        </div>

        <div class="hero-video-box">
          <video class="autoBlur" autoplay loop muted Plays-inline src="videos/hero-video.mp4" type="video/mp4"></video>
        </div>

        <div class="scroll-down"></div>
      </section>

      <!-- Info Section -->
      <section class="info-section">
        <h1 class="info-section autoDisplay">Hello,There !</h1>
        <div class="info-card">
          <div class="card">
            <h1>Hi there, I'm Shimul hp</h1>
            <P>Frontend Developer
              Led the design and implementation of responsive user interfaces for client websites high-performance
              solutions using HTML, CSS </P>

            <img src="images/grid1.png" alt="card-imgae">
          </div>
          <div class="card">
            <h1>UI/UX Designer</h1>
            <P>Creative Digital Media Specialist at Creative Vision Agency</P>

            <img src="images/grid2.png" alt="card-imgae">
          </div>
          <div class="card">
            <h1>Design. Build. Elevate</h1>

            <P>Designing Impactful Media at Creative Vision Agency</P>

            <video autoplay loop muted plays-inline src="videos/glob.mp4" type="video/mp4"></video>

            <button><i class='bx bx-link-external'></i>Contact Me</button>
          </div>
          <div class="card">
            <h1>Building Modern, Scalable Web Solutions with Code & Creativity</h1>
            <P>Software Engineer, Innovatech Solutions Inc. Designed and developed scalable web applications
              using JavaScript, Python, and modern frameworks.</P>

            <img src="images/grid4.png" alt="card-imgae">
          </div>
        </div>
      </section>

      <!-- My Projects Section -->
      <section class="my-pojects">
        <h1 class="section-title autoDisplay">My Projects</h1>
        <div class="poject-card">
          <div class="pojects-vidbox autoBlur">
            <video id="projectvideo1" src="videos/Digital Technology.mp4" muted loop></video>
          </div>

          <div class="pojects-info fadeInRight">
            <h1>Smart IT<span class="gradient"> Evolution </span>Technology</h1>

            <p>represents the future of innovation — where intelligence meets agility. From cloud computing to AI-driven
              solutions, it’s
              about building smarter systems that adapt, scale, and empower progress.</p>

            <button><i class='bx bx-link-external'></i>Website</button>
          </div>
        </div>
        <div class="poject-card">
          <div class="pojects-vidbox autoBlur">
            <video id="projectvideo2" src="videos/project1 mixkit.mp4" muted loop></video>
          </div>

          <div class="pojects-info fadeInRight">
            <h1>Where <span class="gradient">Music Meets </span>Emotion</h1>

            <p>is a place where sound goes beyond hearing — it connects, heals, and inspires. Each note carries a
              feeling, each rhythm tells a story. It’s not just music,
              it’s the voice of emotion.</p>

            <button><i class='bx bx-link-external'></i>Website</button>
          </div>
        </div>
        <div class="poject-card">
          <div class="pojects-vidbox autoBlur">
            <video id="projectvideo3" src="videos/Web Developers and -Designers.mp4" muted loop></video>
          </div>

          <div class="pojects-info fadeInRight">
            <h1>Web <span class="gradient">Developers and </span>Designer</h1>

            <p>Web developers create and maintain websites. They are also responsible for the site's technical aspects,
              such as its performance and capacity, which are measures of a website's speed and how much traffic
              the site can handle. In addition, web developers may create content for the site.</p>

            <button><i class='bx bx-link-external'></i>Website</button>
          </div>
        </div>
      </section>


      <!-- Software and Song Downloads Section -->
      <section class="downloads-section" id="downloads">
        <h1 class="section-title autoDisplay">Downloads Center</h1>
        <p class="section-description">Explore my collection of software tools, operating systems, and music tracks available for download.</p>

        <div class="list">
          <!-- Software Downloads -->
          <div class="download-category">
            <h2 class="category-title gradient">Software <i class='bx bx-code-alt'></i></h2>
            <div class="download-grid">
              <!-- Software Item 1 -->
              <div class="download-card autoBlur">
                <div class="download-icon">
                  <i class='bx bx-code-block'></i>
                </div>
                <div class="download-info">
                  <h2>Code Editor Pro Link</h2>
                  <p>A lightweight yet powerful code editor with syntax highlighting for multiple languages.</p>
                  <div class="download-meta">
                    <span><i class='bx bx-chip'></i> Version 2.1.0</span>
                    <span><i class='bx bx-calendar'></i> May 15, 2023</span>
                    <span><i class='bx bx-download'></i> 2.5K</span>
                  </div>
                  <a href="https://visualstudio.microsoft.com/" class="download-button"><i class='bx bx-download'></i> Download</a>
                </div>
              </div>

              <!-- Software Item 2 -->
              <div class="download-card autoBlur">
                <div class="download-icon">
                  <i class='bx bx-image'></i>
                </div>
                <div class="download-info">
                  <h2>Image Optimizer Link</h2>
                  <p>Batch compress and optimize images without losing quality. Perfect for web developers.</p>
                  <div class="download-meta">
                    <span><i class='bx bx-chip'></i> Version 1.5.2</span>
                    <span><i class='bx bx-calendar'></i> June 3, 2023</span>
                    <span><i class='bx bx-download'></i> 1.8K</span>
                  </div>
                  <a href="https://tinypng.com/" class="download-button"><i class='bx bx-download'></i> Download</a>
                </div>
              </div>
            </div>
          </div>

          <!-- Windows OS Downloads -->
          <div class="download-category">
            <h2 class="category-title gradient">Windows OS <i class='bx bxl-windows'></i></h2>
            <div class="download-grid">
              <!-- Windows Item 1 -->
              <div class="download-card autoBlur">
                <div class="download-icon windows-icon">
                  <i class='bx bxl-windows'></i>
                </div>
                <div class="download-info">
                  <h2>Windows 11 24H2</h2>
                  <p>The latest Windows 11 update with enhanced features, improved performance, and a refined user interface.</p>
                  <div class="download-meta">
                    <span><i class='bx bx-chip'></i> Version 24H2</span>
                    <span><i class='bx bx-calendar'></i> October 2023</span>
                    <span><i class='bx bx-hdd'></i> 5.1 GB</span>
                  </div>
                  <a href="https://www.microsoft.com/software-download/windows11" class="download-button" target="_blank"><i class='bx bx-download'></i> Download ISO</a>
                </div>
              </div>

              <!-- Windows Item 2 -->
              <div class="download-card autoBlur">
                <div class="download-icon windows-icon">
                  <i class='bx bxl-windows'></i>
                </div>
                <div class="download-info">
                  <h2>Windows 10 Enterprise</h2>
                  <p>A stable and secure Windows version designed for business environments with advanced management features.</p>
                  <div class="download-meta">
                    <span><i class='bx bx-chip'></i> Version 21H2</span>
                    <span><i class='bx bx-calendar'></i> May 18, 2023</span>
                    <span><i class='bx bx-hdd'></i> 4.8 GB</span>
                  </div>
                  <a href="https://www.microsoft.com/en-us/evalcenter/download-windows-10-enterprise" class="download-button" target="_blank"><i class='bx bx-download'></i> Download ISO</a>
                </div>
              </div>
            </div>
          </div>

          <!-- Linux OS Downloads -->
          <div class="download-category">
            <h2 class="category-title gradient">Linux OS <i class='bx bxl-tux'></i></h2>
            <div class="download-grid">
              <!-- Linux Item 1 -->
              <div class="download-card autoBlur">
                <div class="download-icon linux-icon">
                  <i class='bx bxl-ubuntu'></i>
                </div>
                <div class="download-info">
                  <h2>Ubuntu 22.04 LTS Link</h2>
                  <p>A popular Linux distribution with long-term support, perfect for both desktop and server use.</p>
                  <div class="download-meta">
                    <span><i class='bx bx-chip'></i> Version 22.04.3</span>
                    <span><i class='bx bx-calendar'></i> August 10, 2023</span>
                    <span><i class='bx bx-hdd'></i> 3.5 GB</span>
                  </div>
                  <a href="https://ubuntu.com/download/desktop" class="download-button"><i class='bx bx-download'></i> Download ISO</a>
                </div>
              </div>

              <!-- Linux Item 2 -->
              <div class="download-card autoBlur">
                <div class="download-icon linux-icon">
                  <i class='bx bxl-debian'></i>
                </div>
                <div class="download-info">
                  <h2>Debian 12 Link</h2>
                  <p>A stable and secure Linux distribution known for its reliability and extensive software repository.</p>
                  <div class="download-meta">
                    <span><i class='bx bx-chip'></i> Version 12.1</span>
                    <span><i class='bx bx-calendar'></i> July 22, 2023</span>
                    <span><i class='bx bx-hdd'></i> 4.2 GB</span>
                  </div>
                  <a href="https://www.debian.org/distrib/" class="download-button"><i class='bx bx-download'></i> Download ISO</a>
                </div>
              </div>
            </div>
          </div>

          <!-- Music Downloads -->
          <div class="download-category">
            <h2 class="category-title gradient">Music <i class='bx bx-music'></i></h2>
            <div class="download-grid">
              <!-- Music Item 1 -->
              <div class="download-card autoBlur">
                <div class="download-icon music-icon">
                  <i class='bx bx-music'></i>
                </div>
                <div class="download-info">
                  <h2>Leja Leja</h2>
                  <p>A beautiful melodic track with soothing vocals and captivating rhythm.</p>
                  <div class="audio-player">
                    <audio controls>
                      <source src="./Audio/Leja Leja.mp3" type="audio/mpeg">
                      Your browser does not support the audio element.
                    </audio>
                  </div>
                  <div class="download-meta">
                    <span><i class='bx bx-time'></i> 3:45</span>
                    <span><i class='bx bx-calendar'></i> July 12, 2023</span>
                    <span><i class='bx bx-download'></i> 1.2K</span>
                  </div>
                  <a href="./Audio/Leja Leja.mp3" class="download-button"><i class='bx bx-download'></i> Download MP3</a>
                </div>
              </div>

              <!-- Music Item 2 -->
              <div class="download-card autoBlur">
                <div class="download-icon music-icon">
                  <i class='bx bx-music'></i>
                </div>
                <div class="download-info">
                  <h2>David Guetta - I'm Good (Blue)</h2>
                  <p>An energetic dance track featuring Bebe Rexha with catchy beats and uplifting vibes.</p>
                  <div class="audio-player">
                    <audio controls>
                      <source src="./Audio/David Guetta & Bebe Rexha - I'm Good (Blue).mp3" type="audio/mpeg">
                      Your browser does not support the audio element.
                    </audio>
                  </div>
                  <div class="download-meta">
                    <span><i class='bx bx-time'></i> 4:22</span>
                    <span><i class='bx bx-calendar'></i> August 5, 2023</span>
                    <span><i class='bx bx-download'></i> 950</span>
                  </div>
                  <a href="./Audio/David Guetta & Bebe Rexha - I'm Good (Blue).mp3" class="download-button"><i class='bx bx-download'></i> Download MP3</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>


      <section class="skills-section">
        <h1 class="section-title autoDisplay">
          My Skills
        </h1>

        <div class="skills-box">
          <img class="skills-image" src="images/digital brain.png" alt="skills-image">

          <div class="designer autoDisplay">
            <h1 class="gradient">Designer <i class='bx bx-laptop'></i></h1>
            <P>"I have expertise in HTML, CSS, and JavaScript, with a strong eye for detail and a passion for clean,
              responsive design. I'm also well-versed in design tools like Figma and Adobe XD, which allows me to bridge
              the gap between design and development seamlessly. My strength lies in turning creative ideas into
              interactive, user-friendly
              experiences that not only look great but also perform flawlessly across all devices."</P>
          </div>
          <div class="coder autoDisplay">
            <h1 class="gradient">code <i class='bx bx-code-block'></i></h1>
            <P>"I'm skilled in HTML, CSS, and JavaScript, along with frameworks like React and Node.js, and databases
              such as MongoDB and MySQL. With a strong foundation in both front-end and back-end development, I build
              responsive,
              dynamic web applications that deliver seamless user experiences. I also have a keen eye for
              design and enjoy transforming ideas into interactive, real-world solutions."</P>
          </div>

          <div class="slider" reverse="true" style="
          --width:100px;
          --height:100px;
          --quantity: 9;
          ">
            <div class="list">
              <div class="item" style="--position:1"><img src="images/1.png" alt="slider-image"></div>
              <div class="item" style="--position:2"><img src="images/2.png" alt="slider-image"></div>
              <div class="item" style="--position:3"><img src="images/3.png" alt="slider-image"></div>
              <div class="item" style="--position:4"><img src="images/4.png" alt="slider-image"></div>
              <div class="item" style="--position:5"><img src="images/5.png" alt="slider-image"></div>
              <div class="item" style="--position:6"><img src="images/6.png" alt="slider-image"></div>
              <div class="item" style="--position:7"><img src="images/7.png" alt="slider-image"></div>
              <div class="item" style="--position:8"><img src="images/8.png" alt="slider-image"></div>
              <div class="item" style="--position:9"><img src="images/9.png" alt="slider-image"></div>
            </div>

          </div>
        </div>
      </section>


      <section class="about-section" id="about">
        <h1 class="section-title autoDisplay" data-aos="fade-up">About Me</h1>
        <div class="about-content autoBlur">
          <img src="images/MyLogo.jpg" alt="Shimul HP" class="about-photo" style="width:120px; border-radius:50%; margin-bottom:20px;">
          <div>
            <p data-aos="fade-right" data-aos-delay="100">
              Hi! I'm <strong>Shimul HP</strong>, a creative and detail-oriented frontend developer who builds interactive and visually engaging websites. I specialize in using HTML, CSS, JavaScript, and GSAP to bring static designs to life.
            </p>
            <p data-aos="fade-right" data-aos-delay="300">
              I'm passionate about creating user-friendly, performance-optimized websites that not only look great but also feel smooth and modern. From sleek landing pages to fully responsive portfolios, I focus on delivering clean, efficient code and memorable user experiences.
            </p>
            <p data-aos="fade-right" data-aos-delay="500">
              Currently, I'm exploring React to take my frontend skills to the next level. Whether it's a personal brand site, a creative portfolio, or an interactive animation — I enjoy turning ideas into reality on the web.
            </p>
            <p data-aos="fade-right" data-aos-delay="700">
              If you're looking for someone who cares about both aesthetics and functionality, I'm always open to collaboration and new opportunities!
            </p>
          </div>
        </div>
        <div>
          <span id="about-typing"></span>
        </div>
      </section>

      <section class="contact-section" id="contact">
        <h1 class="section-title autoDisplay">Let's talk</h1>

        <div class="social-box autoBlur">
          <a href="mailto:<EMAIL>"><i class='bx bx-envelope'></i><EMAIL></a>
          <a href="mailto:<EMAIL>"><i class='bx bx-envelope'></i><EMAIL></a>

          <div class="social-icons autoBlur">
            <a href="https://www.facebook.com/share/163DBHpfz1/"><i class='bx bxl-facebook-circle'></i></a>
            <a href="https://www.instagram.com/sihmulhp?igsh=aGttMTlkdGZqemVo"><i class='bx bxl-instagram-alt'></i></a>
            <a href="https://www.linkedin.com/in/shimul-hp-61059a18a?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app"><i class='bx bxl-linkedin-square'></i></a>
            <a href="https://x.com/Shimulhp100?s=09"><i class='bx bxl-twitter'></i></a>
          </div>
        </div>


        <div class="contact-box">
          <p>Whether you're looking to build a new website, improve your existing
            platform, or bring a unique project to life. I'm here to help.</p>

          <form id="contact-form">
            <div id="form-status" class="hidden"></div>

            <label for="name">Full Name</label>
            <input type="text" id="name" name="name" placeholder="Your Full Name" required>

            <label for="email">Email Address</label>
            <input type="email" id="email" name="email" placeholder="Your Email Address" required>

            <label for="message">Your Message</label>
            <textarea class="input-message" id="message" name="message" placeholder="Share your thoughts..." required></textarea>

            <button type="submit"><i class='bx bx-send'></i>Send Message</button>
          </form>
        </div>
      </section>

      <!-- AI Chat Board -->
      <div id="ai-chat-board">
        <div id="ai-chat-header">
          <span class="ai-header-content">
            <i class='bx bx-bot'></i> AI Assistant
          </span>
          <span id="ai-chat-close">&times;</span>
        </div>
        <div id="ai-chat-messages"></div>
        <form id="ai-chat-form">
          <input type="text" id="ai-chat-input" placeholder="Type your message..." autocomplete="off" required />
          <button type="submit">Send</button>
        </form>
      </div>

      <!-- Gemini Voice Call Interface -->
      <div id="gemini-voice-call" class="voice-call-interface hidden">
        <div class="voice-call-header">
          <div class="call-info">
            <i class='bx bx-microphone-alt'></i>
            <span>Live Voice Call with Gemini AI</span>
          </div>
          <button id="voice-call-close" class="call-close-btn">&times;</button>
        </div>

        <div class="voice-call-content">
          <div class="voice-avatar">
            <div class="avatar-circle">
              <i class='bx bx-bot'></i>
            </div>
            <div class="voice-waves">
              <span></span><span></span><span></span><span></span><span></span>
            </div>
          </div>

          <div class="call-status">
            <h3 id="call-status-text">Ready to start voice call</h3>
            <p id="call-subtitle">Click the microphone to begin speaking</p>
          </div>

          <div class="voice-transcript">
            <div id="voice-messages"></div>
          </div>
        </div>

        <div class="voice-call-controls">
          <button id="start-voice-call" class="voice-call-btn start">
            <i class='bx bx-microphone'></i>
            <span>Start Call</span>
          </button>
          <button id="mute-voice-call" class="voice-call-btn mute">
            <i class='bx bx-microphone-off'></i>
            <span>Mute</span>
          </button>
          <button id="end-voice-call" class="voice-call-btn end">
            <i class='bx bx-phone-off'></i>
            <span>End Call</span>
          </button>
        </div>
      </div>

      <div id="ai-chat-toggle">
        <i class='bx bx-bot'></i>
      </div>
      <button onclick="openGeminiVoiceChat()" class="gemini-voice-btn">
        <i class='bx bx-microphone'></i> Live Voice Call - Gemini AI
      </button>

      <!-- Footer -->
      <footer>
        <h1>&copy; 2025 <strong>Shimul HP</strong> — Crafted with 💻 and ☕</h1>
      </footer>
    </div>

  </div>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.7/gsap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.7/ScrollTrigger.min.js"></script>
  <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/typeit@8.7.1/dist/typeit.min.js"></script>
  <script src="app.js"></script>
</body>

</html>
