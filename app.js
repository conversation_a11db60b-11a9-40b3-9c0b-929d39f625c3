const hoverSign = document.querySelector('.hover-sign');
const sideBar = document.querySelector('.sidebar');
const menu = document.querySelector('.menu-icon');
const closeBtn = document.querySelector('.close-icon');


// Enhanced mobile sidebar functionality
function openSidebar() {
  sideBar.classList.remove("close-sidebar");
  sideBar.classList.add("open-sidebar");
  document.body.style.overflow = 'hidden'; // Prevent background scrolling
}

function closeSidebar() {
  sideBar.classList.remove("open-sidebar");
  sideBar.classList.add("close-sidebar");
  document.body.style.overflow = 'auto'; // Restore scrolling
}

menu.addEventListener("click", openSidebar);
menu.addEventListener("touchstart", openSidebar, { passive: true });

closeBtn.addEventListener("click", closeSidebar);
closeBtn.addEventListener("touchstart", closeSidebar, { passive: true });

// Close sidebar when clicking outside on mobile
document.addEventListener("click", function(event) {
  if (window.innerWidth <= 768 && sideBar.classList.contains("open-sidebar")) {
    if (!sideBar.contains(event.target) && !menu.contains(event.target)) {
      closeSidebar();
    }
  }
});

// Close sidebar when touching outside on mobile
document.addEventListener("touchstart", function(event) {
  if (window.innerWidth <= 768 && sideBar.classList.contains("open-sidebar")) {
    if (!sideBar.contains(event.target) && !menu.contains(event.target)) {
      closeSidebar();
    }
  }
}, { passive: true });

// Close sidebar on escape key
document.addEventListener("keydown", function(event) {
  if (event.key === "Escape" && sideBar.classList.contains("open-sidebar")) {
    closeSidebar();
  }
});

// Auto-close sidebar when clicking navigation links
const sidebarLinks = sideBar.querySelectorAll('a[href^="#"]');
sidebarLinks.forEach(link => {
  link.addEventListener('click', function() {
    if (window.innerWidth <= 768) {
      setTimeout(closeSidebar, 300); // Small delay for smooth transition
    }
  });
});


document.querySelectorAll('a[href="#"]').forEach(a => {
  a.addEventListener('click', e => e.preventDefault());
});


gsap.registerPlugin(ScrollTrigger);
gsap.utils.toArray(".autoBlur").forEach(elem => {
  gsap.fromTo(elem,
    { filter: "blur(40px)", opacity: 0 },
    {
      filter: "blur(0px)",
      opacity: 1,
      scrollTrigger: {
        trigger: elem,
        start: "top 80%",
        end: "top 20%",
        scrub: true
      }
    }
  );
});

// Responsive video play/pause (hover for desktop, tap for mobile)
document.querySelectorAll('.pojects-vidbox video').forEach(video => {
  // Desktop hover
  video.addEventListener('mouseenter', () => {
    video.play();
    if (hoverSign) hoverSign.classList.add("active");
    gsap.to(video, { opacity: 1, duration: 0.5 });
  });

  video.addEventListener('mouseleave', () => {
    video.pause();
    video.currentTime = 0;
    if (hoverSign) hoverSign.classList.remove("active");
    gsap.to(video, { opacity: 0.6, duration: 0.5 });
  });

  // Mobile tap (touchend only)
  const toggleVideo = (e) => {
    e.preventDefault();
    if (video.paused) {
      video.play();
      if (hoverSign) hoverSign.classList.add("active");
      gsap.to(video, { opacity: 1, duration: 0.3 });
    } else {
      video.pause();
      video.currentTime = 0;
      if (hoverSign) hoverSign.classList.remove("active");
      gsap.to(video, { opacity: 0.6, duration: 0.3 });
    }
  };

  // Only add touchend for mobile, click for desktop
  video.addEventListener('click', toggleVideo);
  video.addEventListener('touchend', toggleVideo);
});

AOS.init({
  once: true, // animation happens only once as you scroll
  duration: 900 // animation duration in ms
});

document.addEventListener("DOMContentLoaded", function () {
  // Only trigger typing when About section is in view
  let typedStarted = false;
  function isInViewport(el) {
    const rect = el.getBoundingClientRect();
    return (
      rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.bottom >= 0
    );
  }
  function startTyping() {
    if (typedStarted) return;
    const aboutTyping = document.getElementById('about-typing');
    if (aboutTyping && isInViewport(aboutTyping)) {
      typedStarted = true;
      new TypeIt("#about-typing", {
        speed: 35,
        waitUntilVisible: true,
        cursor: true,
      })
      .type("Hi! I'm <strong>Shimul HP</strong>, a creative and detail-oriented frontend developer who builds interactive and visually engaging websites. I specialize in using HTML, CSS, JavaScript, and GSAP to bring static designs to life.<br><br>")
      .type("I'm passionate about creating user-friendly, performance-optimized websites that not only look great but also feel smooth and modern. From sleek landing pages to fully responsive portfolios, I focus on delivering clean, efficient code and memorable user experiences.<br><br>")
      .type("Currently, I'm exploring React to take my frontend skills to the next level. Whether it's a personal brand site, a creative portfolio, or an interactive animation — I enjoy turning ideas into reality on the web.<br><br>")
      .type("If you're looking for someone who cares about both aesthetics and functionality, I'm always open to collaboration and new opportunities!")
      .go();
    }
  }
  window.addEventListener('scroll', startTyping);
  startTyping();
});

// AI Chat Board Logic
/*
 * =============================================================
 * AI CHAT CONFIGURATION:
 *
 * The chat is now configured with a Gemini API key and is ready to use.
 * It uses the Gemini 2.0 Flash model to generate responses.
 *
 * If you need to change the API key in the future, update the API_KEY
 * constant below (around line 182).
 * =============================================================
 */
document.addEventListener("DOMContentLoaded", function () {
  // Handle fixed header on mobile
  const header = document.querySelector('header');
  let lastScrollTop = 0;

  // Function to handle header visibility on scroll
  function handleHeaderScroll() {
    if (window.innerWidth <= 700) { // Only apply on mobile
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      // If scrolled down more than 100px
      if (scrollTop > 100) {
        // Scrolling down
        if (scrollTop > lastScrollTop) {
          header.classList.add('scrolled');
          header.classList.remove('scrolled-up');
        }
        // Scrolling up
        else {
          header.classList.remove('scrolled');
          header.classList.add('scrolled-up');
        }
      } else {
        // At the top of the page
        header.classList.remove('scrolled');
        header.classList.remove('scrolled-up');
      }

      lastScrollTop = scrollTop;
    }
  }

  // Add scroll event listener
  window.addEventListener('scroll', handleHeaderScroll);

  // Fix for mobile touch events on animated elements
  const animatedElements = document.querySelectorAll('.autoBlur, .autoDisplay, .fadeInRight');
  const projectCards = document.querySelectorAll('.poject-card');
  const projectButtons = document.querySelectorAll('.pojects-info button');
  const socialIcons = document.querySelectorAll('.social-icons a');

  // Add touch event listeners to ensure clickability
  function addTouchSupport(elements) {
    elements.forEach(element => {
      element.style.touchAction = 'auto';
      element.style.pointerEvents = 'auto';

      // Add touch start listener to ensure element is responsive
      element.addEventListener('touchstart', function() {
        // Don't prevent default to allow normal touch behavior
        console.log('Touch detected on element');
      }, { passive: true });
    });
  }

  // Apply touch support to all animated elements
  addTouchSupport(animatedElements);
  addTouchSupport(projectCards);

  // Special handling for social icons
  socialIcons.forEach(icon => {
    // Ensure social icons are clickable
    icon.style.zIndex = '200';
    icon.style.position = 'relative';
    icon.style.pointerEvents = 'auto';
    icon.style.touchAction = 'manipulation';

    // Add specific touch event listeners
    icon.addEventListener('touchstart', function() {
      console.log('Social icon touched:', this.href);
      // Highlight the icon briefly to provide visual feedback
      this.style.transform = 'scale(1.2)';
      setTimeout(() => {
        this.style.transform = 'scale(1)';
      }, 150);
    }, { passive: true });

    // Ensure clicks work properly
    icon.addEventListener('click', function() {
      console.log('Social icon clicked:', this.href);
    });
  });

  // Ensure buttons are extra clickable on mobile
  projectButtons.forEach(button => {
    button.style.zIndex = '100';
    button.style.position = 'relative';
    button.style.pointerEvents = 'auto';

    // Make the touch target area larger
    button.addEventListener('touchstart', function() {
      console.log('Button touched');
    }, { passive: true });
  });

  // Contact Form Handling
  const contactForm = document.getElementById('contact-form');
  const formStatus = document.getElementById('form-status');

  if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();

      // Get form values
      const name = document.getElementById('name').value.trim();
      const email = document.getElementById('email').value.trim();
      const message = document.getElementById('message').value.trim();

      // Simple validation
      if (!name || !email || !message) {
        showFormStatus('error', 'Please fill in all fields');
        return;
      }

      // Email validation
      if (!isValidEmail(email)) {
        showFormStatus('error', 'Please enter a valid email address');
        return;
      }

      // Simulate form submission (replace with actual form submission)
      showFormStatus('success', 'Thank you for your message! I\'ll get back to you soon.');

      // Clear form
      contactForm.reset();

      // In a real implementation, you would send the form data to a server here
      console.log('Form submitted:', { name, email, message });

      // Example of how you would send the form data to a server:
      /*
      fetch('your-server-endpoint', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, email, message }),
      })
      .then(response => response.json())
      .then(data => {
        showFormStatus('success', 'Thank you for your message! I\'ll get back to you soon.');
        contactForm.reset();
      })
      .catch(error => {
        showFormStatus('error', 'There was an error sending your message. Please try again.');
        console.error('Error:', error);
      });
      */
    });
  }

  // Helper function to validate email
  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Helper function to show form status
  function showFormStatus(type, message) {
    if (formStatus) {
      formStatus.textContent = message;
      formStatus.className = type; // 'success' or 'error'
      formStatus.classList.remove('hidden');

      // Hide the status message after 5 seconds
      setTimeout(() => {
        formStatus.classList.add('hidden');
      }, 5000);
    }
  }

  // AI Chat Functionality
  const chatToggle = document.getElementById('ai-chat-toggle');
  const chatBoard = document.getElementById('ai-chat-board');
  const chatClose = document.getElementById('ai-chat-close');
  const chatForm = document.getElementById('ai-chat-form');
  const chatInput = document.getElementById('ai-chat-input');
  const chatMessages = document.getElementById('ai-chat-messages');

  // Check if elements exist to prevent errors
  if (!chatToggle || !chatBoard || !chatClose || !chatForm || !chatInput || !chatMessages) {
    console.error("One or more chat elements not found");
    return;
  }

  // Make sure toggle is visible and chat is hidden initially
  if (chatToggle) chatToggle.style.display = 'flex';
  if (chatBoard) chatBoard.style.display = 'none';

  // Initialize chat - show welcome message
  addMessage('AI', 'Hello! I\'m powered by Gemini AI. How can I help you today?');

  // Variable to track chat state
  let isChatOpen = false;

  // Check if we're on mobile
  const isMobile = () => window.innerWidth <= 768;

  // Toggle chat visibility
  chatToggle.onclick = function() {
    if (isMobile()) {
      // On mobile: toggle open/close with the same button
      if (isChatOpen) {
        // If already open, close it
        isChatOpen = false;
        chatBoard.style.display = 'none';
        chatToggle.classList.remove('active');
        console.log("Chat closed (mobile toggle)");
      } else {
        // If closed, open it
        isChatOpen = true;
        chatBoard.style.display = 'flex';
        console.log("Chat opened (mobile toggle)");
        // On mobile, add active class to indicate open state
        chatToggle.classList.add('active');
      }
    } else {
      // On desktop: original behavior
      isChatOpen = true;
      chatBoard.style.display = 'flex';
      chatToggle.style.display = 'none';
      console.log("Chat opened (desktop)");
    }
  };

  // Close button behavior
  chatClose.onclick = function() {
    isChatOpen = false;
    chatBoard.style.display = 'none';

    // On mobile, keep toggle visible but remove active class
    if (isMobile()) {
      chatToggle.classList.remove('active');
    } else {
      // On desktop, show the toggle button again
      chatToggle.style.display = 'flex';
    }

    console.log("Chat closed via X button");
  };

  // Handle window resize to ensure proper display on all devices
  window.addEventListener('resize', function() {
    // Check if we're switching between mobile and desktop
    const currentMobile = isMobile();

    // Always respect the current open/closed state
    if (isChatOpen) {
      chatBoard.style.display = 'flex';

      if (currentMobile) {
        // On mobile, toggle button is always visible with active class
        chatToggle.classList.add('active');
      } else {
        // On desktop, hide toggle button when chat is open
        chatToggle.style.display = 'none';
        chatToggle.classList.remove('active');
      }
    } else {
      // Chat is closed
      chatBoard.style.display = 'none';
      chatToggle.style.display = 'flex';
      chatToggle.classList.remove('active');
    }
  });

  function addMessage(sender, text) {
    const div = document.createElement('div');
    div.className = sender.toLowerCase() + '-message';
    div.innerHTML = `<strong>${sender}:</strong> ${text}`;
    chatMessages.appendChild(div);
    // After adding a new message:
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  // Fallback responses when API is not available
  const fallbackResponses = [
    "I'm a demo AI assistant. To enable real AI responses, please add your Gemini API key in the app.js file.",
    "This is a simulated response. For real AI interactions, you'll need to set up your API key.",
    "I'm currently in demo mode. To chat with the real AI, please configure your API key.",
    "Hello! I'm a placeholder assistant. The website owner needs to set up the API for me to give real responses.",
    "I can only provide pre-written responses until the API key is configured. Check the app.js file for instructions."
  ];

  // Check if API key is configured
  const API_KEY = "AIzaSyBIIg6QmcuC3qrI_YavYETZQMfYs3gFsvc"; // Your Gemini API key
  const isApiConfigured = true; // API key is now configured

  async function askGemini(question) {
    // If API key is not configured, return a fallback response
    if (!isApiConfigured) {
      console.log("API key not configured. Using fallback response.");
      return getRandomFallbackResponse();
    }

    try {
      console.log("Calling Gemini API with question:", question);

      // Simple responses for testing without API
      const simpleResponses = {
        "hello": "Hello! How can I help you today?",
        "hi": "Hi there! What can I do for you?",
        "how are you": "I'm just a program, but I'm functioning well! How can I assist you?",
        "what is your name": "I'm an AI assistant powered by Gemini. How can I help you today?",
        "who made you": "I was created by Google and integrated into this website by the developer."
      };

      // Check if we have a simple response for this question (case insensitive)
      const lowerQuestion = question.toLowerCase();
      for (const key in simpleResponses) {
        if (lowerQuestion.includes(key)) {
          console.log("Using simple response for:", key);
          return simpleResponses[key];
        }
      }

      // Proceed with API call
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contents: [{ parts: [{ text: question }] }]
        })
      });

      console.log("API response status:", response.status);

      if (!response.ok) {
        let errorInfo = "";
        try {
          const errorData = await response.json();
          errorInfo = JSON.stringify(errorData);
          console.error("API error details:", errorData);

          if (errorData.error && errorData.error.message) {
            if (errorData.error.message.includes("API key")) {
              return "API key error: The API key is invalid or has not been properly set up. Please check your configuration.";
            }
          }
        } catch (e) {
          errorInfo = await response.text();
        }

        console.error(`API error (${response.status}):`, errorInfo);

        if (response.status === 403 || response.status === 401) {
          return "API key error: The API key may be invalid or missing permissions. Please check your configuration.";
        }

        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      console.log("API response data:", data);

      if (data.candidates && data.candidates[0] && data.candidates[0].content &&
          data.candidates[0].content.parts && data.candidates[0].content.parts[0]) {
        return data.candidates[0].content.parts[0].text;
      } else {
        console.error("Unexpected API response format:", data);
        throw new Error("Unexpected API response format");
      }
    } catch (error) {
      console.error("Error calling Gemini API:", error);

      if (error.message.includes("Failed to fetch")) {
        return "Network error: Unable to connect to the AI service. Please check your internet connection.";
      }

      return "Sorry, I encountered an error. Please try again later. (Error: " + error.message + ")";
    }
  }

  // Get a random fallback response
  function getRandomFallbackResponse() {
    const randomIndex = Math.floor(Math.random() * fallbackResponses.length);
    return fallbackResponses[randomIndex];
  }

  chatForm.onsubmit = async function(e) {
    e.preventDefault();
    const msg = chatInput.value.trim();
    if (!msg) return;

    // Add user message
    addMessage('You', msg);
    chatInput.value = '';

    // Add AI thinking message
    addMessage('AI', '<em>Thinking...</em>');

    try {
      // Call Gemini API directly
      const reply = await askGemini(msg);

      // Replace thinking message with actual reply
      chatMessages.lastChild.innerHTML = `<strong>AI:</strong> ${reply}`;
    } catch (error) {
      console.error("Error in chat submission:", error);
      chatMessages.lastChild.innerHTML = `<strong>AI:</strong> Sorry, I encountered an error. Please try again.`;
    }
  };
});

// Improve mobile touch responsiveness for project cards
document.addEventListener("DOMContentLoaded", function() {
  // Fix for project cards on mobile
  const projectCards = document.querySelectorAll('.poject-card');

  projectCards.forEach(card => {
    // Make entire card area clickable on mobile
    if (window.innerWidth <= 768) { // Mobile breakpoint
      const videoBox = card.querySelector('.pojects-vidbox');
      const infoBox = card.querySelector('.pojects-info');
      const button = card.querySelector('.pojects-info button');

      // Ensure both sides are clickable
      [videoBox, infoBox].forEach(element => {
        if (element) {
          element.style.pointerEvents = 'auto';
          element.style.touchAction = 'auto';
          element.style.zIndex = '10';

          // Add touch feedback
          element.addEventListener('touchstart', function() {
            this.style.opacity = '0.9';
            setTimeout(() => {
              this.style.opacity = '1';
            }, 150);
          }, { passive: true });
        }
      });

      // Make sure button is extra clickable
      if (button) {
        button.style.zIndex = '100';
        button.style.position = 'relative';
      }
    }
  });

  // Downloads page functionality
  // Smooth scroll for the "Browse Downloads" button
  const browseButton = document.querySelector('.hero-info button');
  if (browseButton) {
    browseButton.addEventListener('click', function() {
      const softwareSection = document.getElementById('software');
      if (softwareSection) {
        softwareSection.scrollIntoView({ behavior: 'smooth' });
      }
    });
  }

  // Download cards hover effects and animations
  const downloadCards = document.querySelectorAll('.download-card');
  downloadCards.forEach(card => {
    // Add hover animation
    card.addEventListener('mouseenter', function() {
      gsap.to(this, { scale: 1.02, duration: 0.3, ease: 'power1.out' });
    });

    card.addEventListener('mouseleave', function() {
      gsap.to(this, { scale: 1, duration: 0.3, ease: 'power1.out' });
    });

    // Make download buttons more interactive
    const downloadButton = card.querySelector('.download-button');
    if (downloadButton) {
      downloadButton.addEventListener('mouseenter', function() {
        gsap.to(this, { scale: 1.05, duration: 0.2 });
      });

      downloadButton.addEventListener('mouseleave', function() {
        gsap.to(this, { scale: 1, duration: 0.2 });
      });

      // Simulate download (for demo purposes)
      downloadButton.addEventListener('click', function(e) {
        e.preventDefault();

        // Change button text temporarily
        const originalText = this.innerHTML;
        this.innerHTML = '<i class="bx bx-check"></i> Downloaded';

        // Restore original text after 2 seconds
        setTimeout(() => {
          this.innerHTML = originalText;
        }, 2000);
      });
    }

    // Mobile touch support for download cards
    if (window.innerWidth <= 768) {
      card.style.pointerEvents = 'auto';
      card.style.touchAction = 'auto';

      card.addEventListener('touchstart', function() {
        this.style.transform = 'scale(1.01)';
        setTimeout(() => {
          this.style.transform = 'scale(1)';
        }, 150);
      }, { passive: true });
    }
  });

  // Enhanced device detection for comprehensive responsiveness
  function getDeviceType() {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const userAgent = navigator.userAgent;
    const pixelRatio = window.devicePixelRatio || 1;

    // Device detection logic
    const devices = {
      // Large Desktop and 4K
      largeDesktop: width >= 1920,
      desktop: width >= 1200 && width < 1920,
      standardDesktop: width >= 1001 && width < 1200,

      // Tablets
      iPadPro: (width >= 1024 && height >= 1366) || (width >= 1366 && height >= 1024),
      largTablet: width >= 768 && width <= 1024,
      standardTablet: width >= 701 && width <= 767,

      // Mobile Phones
      iPhone14ProMax: (width >= 414 && width <= 430 && height >= 896) || userAgent.includes('iPhone'),
      iPhone13_14: width >= 390 && width <= 414 && height >= 844,
      iPhoneMini: width >= 360 && width <= 375 && height >= 812,
      galaxyS23Ultra: (width >= 400 && width <= 430 && height >= 850) || userAgent.includes('SM-S918'),

      // General categories
      mobile: width <= 768,
      tablet: width > 768 && width <= 1024,
      desktop: width > 1024,

      // High DPI displays
      highDPI: pixelRatio >= 2,

      // Orientation
      landscape: width > height,
      portrait: height > width
    };

    return devices;
  }

  // Apply device-specific optimizations
  function applyDeviceOptimizations() {
    const device = getDeviceType();
    const body = document.body;

    // Add device classes to body for CSS targeting
    if (device.iPhone14ProMax) body.classList.add('iphone-14-pro-max');
    if (device.iPhone13_14) body.classList.add('iphone-13-14');
    if (device.iPhoneMini) body.classList.add('iphone-mini');
    if (device.galaxyS23Ultra) body.classList.add('galaxy-s23-ultra');
    if (device.iPadPro) body.classList.add('ipad-pro');
    if (device.largTablet) body.classList.add('large-tablet');
    if (device.highDPI) body.classList.add('high-dpi');
    if (device.landscape) body.classList.add('landscape');
    if (device.portrait) body.classList.add('portrait');

    // Device-specific optimizations
    if (device.mobile) {
      // Mobile optimizations
      document.body.style.touchAction = 'manipulation';
      document.documentElement.style.scrollBehavior = 'smooth';

      // Optimize touch targets
      const interactiveElements = document.querySelectorAll('button, a, input, .download-button, .menu-icon, .close-icon');
      interactiveElements.forEach(element => {
        element.style.minHeight = '44px';
        element.style.minWidth = '44px';
        element.style.touchAction = 'manipulation';
      });
    }

    if (device.highDPI) {
      // High DPI optimizations
      const videos = document.querySelectorAll('video');
      videos.forEach(video => {
        video.style.willChange = 'transform';
        video.style.backfaceVisibility = 'hidden';
      });
    }

    if (device.tablet) {
      // Tablet-specific optimizations
      const sidebar = document.querySelector('.sidebar');
      if (sidebar && device.landscape) {
        sidebar.style.width = '350px';
      }
    }

    console.log('Device optimizations applied for:', Object.keys(device).filter(key => device[key]));
  }

  // Legacy function for backward compatibility
  function isGalaxyS23Ultra() {
    return getDeviceType().galaxyS23Ultra;
  }

  if (isGalaxyS23Ultra()) {
    // Enhanced touch responsiveness for Galaxy S23 Ultra
    document.body.style.touchAction = 'manipulation';

    // Optimize scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Enhanced button touch targets
    const buttons = document.querySelectorAll('button, .download-button, .pojects-info button');
    buttons.forEach(button => {
      button.style.minHeight = '48px';
      button.style.minWidth = '48px';
      button.style.touchAction = 'manipulation';
    });

    // Optimize video playback for Galaxy S23 Ultra
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
      video.style.willChange = 'transform';
      video.style.backfaceVisibility = 'hidden';
    });

    // Enhanced AI chat for Galaxy S23 Ultra
    const chatBoard = document.getElementById('ai-chat-board');
    const chatToggle = document.getElementById('ai-chat-toggle');

    if (chatBoard && chatToggle) {
      chatToggle.style.touchAction = 'manipulation';
      chatBoard.style.willChange = 'transform';

      // Optimize chat positioning for Galaxy S23 Ultra
      if (window.innerWidth <= 430) {
        chatBoard.style.borderRadius = '20px';
        chatToggle.style.borderRadius = '50%';
      }
    }

    // Optimize form inputs for Galaxy S23 Ultra
    const inputs = document.querySelectorAll('input, textarea');
    inputs.forEach(input => {
      input.style.fontSize = '16px'; // Prevents zoom on focus
      input.style.touchAction = 'manipulation';
    });

    console.log('Galaxy S23 Ultra optimizations applied');
  }

  // Apply comprehensive device optimizations
  applyDeviceOptimizations();

  // Re-apply optimizations on window resize
  let resizeTimeout;
  window.addEventListener('resize', function() {
    // Debounce resize events
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(applyDeviceOptimizations, 250);
  });

  // Optimize viewport meta tag for better mobile experience
  function optimizeViewport() {
    let viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport) {
      viewport = document.createElement('meta');
      viewport.name = 'viewport';
      document.head.appendChild(viewport);
    }

    const device = getDeviceType();
    if (device.mobile) {
      viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover';
    } else if (device.tablet) {
      viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=3.0, user-scalable=yes';
    } else {
      viewport.content = 'width=device-width, initial-scale=1.0';
    }
  }

  optimizeViewport();

  // Contact Form Handler
  const contactForm = document.getElementById('contact-form');
  const formStatus = document.getElementById('form-status');

  if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();

      // Get form data
      const formData = new FormData(contactForm);
      const name = formData.get('name');
      const email = formData.get('email');
      const message = formData.get('message');

      // Create mailto link with form data
      const subject = encodeURIComponent(`Contact from ${name} - Website Inquiry`);
      const body = encodeURIComponent(`
Name: ${name}
Email: ${email}

Message:
${message}

---
Sent from your website contact form
      `);

      // Create mailto URL with your email addresses
      const mailtoLink = `mailto:<EMAIL>,<EMAIL>?subject=${subject}&body=${body}`;

      // Show success message
      showFormStatus('Opening your email client...', 'success');

      // Open email client
      window.location.href = mailtoLink;

      // Reset form after a short delay
      setTimeout(() => {
        contactForm.reset();
        hideFormStatus();
      }, 2000);
    });
  }

  // Form status functions
  function showFormStatus(message, type) {
    if (formStatus) {
      formStatus.textContent = message;
      formStatus.className = `form-status ${type}`;
      formStatus.style.display = 'block';
    }
  }

  function hideFormStatus() {
    if (formStatus) {
      formStatus.style.display = 'none';
    }
  }
});

// Function to open Gemini chat from the button
function openGeminiChat() {
  const chatToggle = document.getElementById('ai-chat-toggle');
  const chatBoard = document.getElementById('ai-chat-board');
  const chatInput = document.getElementById('ai-chat-input');

  // Open the chat board
  if (chatBoard && chatToggle) {
    chatBoard.style.display = 'flex';

    // Check if we're on mobile
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
      // On mobile, add active class to toggle button
      chatToggle.classList.add('active');
    } else {
      // On desktop, hide the toggle button
      chatToggle.style.display = 'none';
    }

    // Focus on the input field for immediate typing
    if (chatInput) {
      setTimeout(() => {
        chatInput.focus();
      }, 300);
    }

    // Add a welcome message specifically for Gemini button users
    const chatMessages = document.getElementById('ai-chat-messages');
    if (chatMessages && chatMessages.children.length <= 1) {
      // Only add if there's just the initial welcome message
      const div = document.createElement('div');
      div.className = 'ai-message';
      div.innerHTML = `<strong>AI:</strong> <em>🤖 Gemini AI activated! I'm powered by Google's latest AI model. What would you like to know?</em>`;
      chatMessages.appendChild(div);
      chatMessages.scrollTop = chatMessages.scrollHeight;
    }
  }
}
