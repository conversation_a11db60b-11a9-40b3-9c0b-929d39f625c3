/**
 * ========================================
 * POWERFUL APP.JS - PRODUCTION READY
 * ========================================
 * Enhanced for hosting environments with:
 * - Error handling & fallbacks
 * - Performance optimizations
 * - Cross-browser compatibility
 * - Mobile-first approach
 * - Debugging capabilities
 * ========================================
 */

// Global error handler
window.addEventListener('error', function(e) {
  console.error('🚨 Global Error:', e.error);
  // Don't let errors break the site
  return true;
});

// Performance monitoring
const performanceStart = performance.now();

// Safe DOM element selection with fallbacks
function safeQuerySelector(selector, context = document) {
  try {
    return context.querySelector(selector);
  } catch (error) {
    console.warn(`⚠️ Selector failed: ${selector}`, error);
    return null;
  }
}

function safeQuerySelectorAll(selector, context = document) {
  try {
    return context.querySelectorAll(selector);
  } catch (error) {
    console.warn(`⚠️ Selector failed: ${selector}`, error);
    return [];
  }
}

// Core elements with safe selection
const elements = {
  hoverSign: safeQuerySelector('.hover-sign'),
  sideBar: safeQuerySelector('.sidebar'),
  menu: safeQuerySelector('.menu-icon'),
  closeBtn: safeQuerySelector('.close-icon'),
  header: safeQuerySelector('header'),
  chatToggle: safeQuerySelector('#ai-chat-toggle'),
  chatBoard: safeQuerySelector('#ai-chat-board'),
  contactForm: safeQuerySelector('#contact-form')
};

// Debug mode for development
const DEBUG = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

function debugLog(message, data = null) {
  if (DEBUG) {
    console.log(`🔧 ${message}`, data || '');
  }
}


// ========================================
// ENHANCED SIDEBAR SYSTEM
// ========================================

function initializeSidebar() {
  try {
    if (!elements.sideBar || !elements.menu || !elements.closeBtn) {
      debugLog('Sidebar elements missing, skipping initialization');
      return false;
    }

    debugLog('Initializing sidebar system');

    // Enhanced sidebar functions with error handling
    function openSidebar() {
      try {
        elements.sideBar.classList.remove("close-sidebar");
        elements.sideBar.classList.add("open-sidebar");
        document.body.style.overflow = 'hidden';
        debugLog('Sidebar opened');
      } catch (error) {
        console.error('Error opening sidebar:', error);
      }
    }

    function closeSidebar() {
      try {
        elements.sideBar.classList.remove("open-sidebar");
        elements.sideBar.classList.add("close-sidebar");
        document.body.style.overflow = '';
        debugLog('Sidebar closed');
      } catch (error) {
        console.error('Error closing sidebar:', error);
      }
    }

    // Event listeners with error handling
    function addSafeEventListener(element, event, handler, options = {}) {
      if (element && typeof handler === 'function') {
        try {
          element.addEventListener(event, handler, options);
          return true;
        } catch (error) {
          console.error(`Failed to add ${event} listener:`, error);
          return false;
        }
      }
      return false;
    }

    // Menu click events
    addSafeEventListener(elements.menu, 'click', openSidebar);
    addSafeEventListener(elements.menu, 'touchstart', openSidebar, { passive: true });

    // Close button events
    addSafeEventListener(elements.closeBtn, 'click', closeSidebar);
    addSafeEventListener(elements.closeBtn, 'touchstart', closeSidebar, { passive: true });

    // Auto-close functionality
    const sidebarLinks = safeQuerySelectorAll('a[href^="#"]', elements.sideBar);
    sidebarLinks.forEach(link => {
      addSafeEventListener(link, 'click', function() {
        if (window.innerWidth <= 768) {
          setTimeout(closeSidebar, 300);
        }
      });
    });

    // Outside click/touch close
    addSafeEventListener(document, 'click', function(event) {
      if (!event || !event.target) return;

      if (window.innerWidth <= 768 && elements.sideBar.classList.contains("open-sidebar")) {
        if (!elements.sideBar.contains(event.target) && !elements.menu.contains(event.target)) {
          closeSidebar();
        }
      }
    });

    addSafeEventListener(document, 'touchstart', function(event) {
      if (!event || !event.target) return;

      if (window.innerWidth <= 768 && elements.sideBar.classList.contains("open-sidebar")) {
        if (!elements.sideBar.contains(event.target) && !elements.menu.contains(event.target)) {
          closeSidebar();
        }
      }
    }, { passive: true });

    // Escape key close
    addSafeEventListener(document, 'keydown', function(event) {
      if (!event) return;

      if (event.key === "Escape" && elements.sideBar.classList.contains("open-sidebar")) {
        closeSidebar();
      }
    });

    debugLog('Sidebar system initialized successfully');
    return true;

  } catch (error) {
    console.error('Failed to initialize sidebar:', error);
    return false;
  }
}

// ========================================
// ENHANCED ANIMATIONS & SCROLL EFFECTS
// ========================================

function initializeAnimations() {
  try {
    debugLog('Initializing animations');

    // Check if GSAP is available
    if (typeof gsap === 'undefined') {
      console.warn('⚠️ GSAP not loaded, skipping GSAP animations');
      return false;
    }

    // Register ScrollTrigger plugin safely
    if (gsap.registerPlugin && typeof ScrollTrigger !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger);
      debugLog('GSAP ScrollTrigger registered');
    }

    // Auto blur animations with error handling
    const autoBlurElements = safeQuerySelectorAll(".autoBlur");
    if (autoBlurElements.length > 0) {
      autoBlurElements.forEach(elem => {
        try {
          gsap.fromTo(elem,
            { filter: "blur(40px)", opacity: 0 },
            {
              filter: "blur(0px)",
              opacity: 1,
              scrollTrigger: {
                trigger: elem,
                start: "top 80%",
                end: "top 20%",
                scrub: true
              }
            }
          );
        } catch (error) {
          console.warn('Failed to animate element:', elem, error);
        }
      });
      debugLog(`Initialized ${autoBlurElements.length} blur animations`);
    }

    // AOS initialization with fallback
    if (typeof AOS !== 'undefined') {
      AOS.init({
        once: true,
        duration: 900,
        disable: function() {
          // Disable on mobile if performance is poor
          return window.innerWidth < 768 && window.navigator.hardwareConcurrency < 4;
        }
      });
      debugLog('AOS initialized');
    } else {
      console.warn('⚠️ AOS not loaded, skipping AOS animations');
    }

    // Prevent default for placeholder links
    const placeholderLinks = safeQuerySelectorAll('a[href="#"]:not(.download-button)');
    placeholderLinks.forEach(a => {
      a.addEventListener('click', e => e.preventDefault());
    });

    debugLog('Animations initialized successfully');
    return true;

  } catch (error) {
    console.error('Failed to initialize animations:', error);
    return false;
  }
}


// ========================================
// MAIN INITIALIZATION SYSTEM
// ========================================

// Wait for DOM to be ready
function initializeApp() {
  try {
    debugLog('🚀 Starting app initialization');

    const initResults = {
      sidebar: initializeSidebar(),
      animations: initializeAnimations(),
      videos: initializeVideoControls(),
      downloads: initializeDownloads(),
      contact: initializeContactForm(),
      typing: initializeTypingEffect(),
      chat: initializeChatSystem(),
      mobile: initializeMobileOptimizations()
    };

    // Log initialization results
    const successful = Object.values(initResults).filter(Boolean).length;
    const total = Object.keys(initResults).length;

    console.log(`✅ App initialized: ${successful}/${total} systems loaded`);

    if (successful < total) {
      console.warn('⚠️ Some systems failed to initialize:', initResults);
    }

    // Performance logging
    const loadTime = performance.now() - performanceStart;
    debugLog(`Performance: App loaded in ${loadTime.toFixed(2)}ms`);

    return initResults;

  } catch (error) {
    console.error('❌ Critical error during app initialization:', error);
    return false;
  }
}

// Enhanced video controls
function initializeVideoControls() {
  try {
    debugLog('Initializing video controls');

    const projectVideos = safeQuerySelectorAll('.pojects-vidbox video');

    if (projectVideos.length === 0) {
      debugLog('No project videos found');
      return true; // Not an error, just no videos
    }

    projectVideos.forEach(video => {
      if (!video) return;

      // Enhanced video toggle function
      const toggleVideo = (e) => {
        try {
          if (e && e.preventDefault) {
            e.preventDefault();
          }

          if (video.paused) {
            video.play().catch(err => debugLog('Video play failed:', err));
            if (elements.hoverSign) elements.hoverSign.classList.add("active");
            if (typeof gsap !== 'undefined') {
              gsap.to(video, { opacity: 1, duration: 0.3 });
            }
          } else {
            video.pause();
            video.currentTime = 0;
            if (elements.hoverSign) elements.hoverSign.classList.remove("active");
            if (typeof gsap !== 'undefined') {
              gsap.to(video, { opacity: 0.6, duration: 0.3 });
            }
          }
        } catch (error) {
          debugLog('Video toggle error:', error);
        }
      };

      // Desktop hover events
      video.addEventListener('mouseenter', () => {
        try {
          video.play().catch(err => debugLog('Video play failed:', err));
          if (elements.hoverSign) elements.hoverSign.classList.add("active");
          if (typeof gsap !== 'undefined') {
            gsap.to(video, { opacity: 1, duration: 0.5 });
          }
        } catch (error) {
          debugLog('Video mouseenter error:', error);
        }
      });

      video.addEventListener('mouseleave', () => {
        try {
          video.pause();
          video.currentTime = 0;
          if (elements.hoverSign) elements.hoverSign.classList.remove("active");
          if (typeof gsap !== 'undefined') {
            gsap.to(video, { opacity: 0.6, duration: 0.5 });
          }
        } catch (error) {
          debugLog('Video mouseleave error:', error);
        }
      });

      // Mobile touch events
      video.addEventListener('click', toggleVideo);
      video.addEventListener('touchend', toggleVideo);
    });

    debugLog(`Video controls initialized for ${projectVideos.length} videos`);
    return true;

  } catch (error) {
    console.error('Failed to initialize video controls:', error);
    return false;
  }
}

// Enhanced downloads system
function initializeDownloads() {
  try {
    debugLog('Initializing downloads system');

    const downloadCards = safeQuerySelectorAll('.download-card');

    if (downloadCards.length === 0) {
      debugLog('No download cards found');
      return true;
    }

    downloadCards.forEach(card => {
      if (!card) return;

      // Add hover animations
      card.addEventListener('mouseenter', function() {
        if (typeof gsap !== 'undefined') {
          gsap.to(this, { scale: 1.02, duration: 0.3, ease: 'power1.out' });
        }
      });

      card.addEventListener('mouseleave', function() {
        if (typeof gsap !== 'undefined') {
          gsap.to(this, { scale: 1, duration: 0.3, ease: 'power1.out' });
        }
      });

      // Enhanced download button functionality
      const downloadButton = card.querySelector('.download-button');
      if (downloadButton) {
        downloadButton.addEventListener('mouseenter', function() {
          if (typeof gsap !== 'undefined') {
            gsap.to(this, { scale: 1.05, duration: 0.2 });
          }
        });

        downloadButton.addEventListener('mouseleave', function() {
          if (typeof gsap !== 'undefined') {
            gsap.to(this, { scale: 1, duration: 0.2 });
          }
        });

        // Smart download handling
        downloadButton.addEventListener('click', function(e) {
          const href = this.getAttribute('href');

          // Handle different link types
          if (href && (href.startsWith('http') && !href.includes('.mp3') && !href.includes('.zip') && !href.includes('.exe'))) {
            return; // Let external links work normally
          }

          // For actual file downloads
          if (href && (href.includes('.mp3') || href.includes('.zip') || href.includes('.exe') || href.startsWith('./Audio/'))) {
            this.setAttribute('download', '');

            const originalText = this.innerHTML;
            this.innerHTML = '<i class="bx bx-check"></i> Downloaded';

            setTimeout(() => {
              this.innerHTML = originalText;
            }, 2000);

            return;
          }

          // For demo links
          if (!href || href === '#') {
            e.preventDefault();

            const originalText = this.innerHTML;
            this.innerHTML = '<i class="bx bx-info-circle"></i> Demo Link';

            setTimeout(() => {
              this.innerHTML = originalText;
            }, 2000);
          }
        });
      }

      // Mobile touch support
      if (window.innerWidth <= 768) {
        card.style.pointerEvents = 'auto';
        card.style.touchAction = 'auto';

        card.addEventListener('touchstart', function() {
          this.style.transform = 'scale(1.01)';
          setTimeout(() => {
            this.style.transform = 'scale(1)';
          }, 150);
        }, { passive: true });
      }
    });

    debugLog(`Downloads system initialized for ${downloadCards.length} cards`);
    return true;

  } catch (error) {
    console.error('Failed to initialize downloads:', error);
    return false;
  }
}

// Enhanced contact form
function initializeContactForm() {
  try {
    debugLog('Initializing contact form');

    if (!elements.contactForm) {
      debugLog('Contact form not found');
      return true;
    }

    const formStatus = safeQuerySelector('#form-status');

    elements.contactForm.addEventListener('submit', function(e) {
      e.preventDefault();

      try {
        const formData = new FormData(elements.contactForm);
        const name = formData.get('name')?.trim();
        const email = formData.get('email')?.trim();
        const message = formData.get('message')?.trim();

        // Validation
        if (!name || !email || !message) {
          showFormStatus('Please fill in all fields', 'error');
          return;
        }

        if (!isValidEmail(email)) {
          showFormStatus('Please enter a valid email address', 'error');
          return;
        }

        // Create mailto link
        const subject = encodeURIComponent(`Contact from ${name} - Website Inquiry`);
        const body = encodeURIComponent(`
Name: ${name}
Email: ${email}

Message:
${message}

---
Sent from your website contact form
        `);

        const mailtoLink = `mailto:<EMAIL>,<EMAIL>?subject=${subject}&body=${body}`;

        showFormStatus('Opening your email client...', 'success');
        window.location.href = mailtoLink;

        setTimeout(() => {
          elements.contactForm.reset();
          hideFormStatus();
        }, 2000);

      } catch (error) {
        console.error('Contact form error:', error);
        showFormStatus('An error occurred. Please try again.', 'error');
      }
    });

    function isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    }

    function showFormStatus(message, type) {
      if (formStatus) {
        formStatus.textContent = message;
        formStatus.className = `form-status ${type}`;
        formStatus.style.display = 'block';
      }
    }

    function hideFormStatus() {
      if (formStatus) {
        formStatus.style.display = 'none';
      }
    }

    debugLog('Contact form initialized successfully');
    return true;

  } catch (error) {
    console.error('Failed to initialize contact form:', error);
    return false;
  }
}

// Enhanced typing effect
function initializeTypingEffect() {
  try {
    debugLog('Initializing typing effect');

    if (typeof TypeIt === 'undefined') {
      console.warn('⚠️ TypeIt not loaded, skipping typing effect');
      return false;
    }

    let typedStarted = false;
    const aboutTyping = safeQuerySelector('#about-typing');

    if (!aboutTyping) {
      debugLog('About typing element not found');
      return true;
    }

    function isInViewport(el) {
      const rect = el.getBoundingClientRect();
      return (
        rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.bottom >= 0
      );
    }

    function startTyping() {
      if (typedStarted || !isInViewport(aboutTyping)) return;

      typedStarted = true;

      try {
        new TypeIt("#about-typing", {
          speed: 35,
          waitUntilVisible: true,
          cursor: true,
        })
        .type("Hi! I'm <strong>Shimul HP</strong>, a creative and detail-oriented frontend developer who builds interactive and visually engaging websites. I specialize in using HTML, CSS, JavaScript, and GSAP to bring static designs to life.<br><br>")
        .type("I'm passionate about creating user-friendly, performance-optimized websites that not only look great but also feel smooth and modern. From sleek landing pages to fully responsive portfolios, I focus on delivering clean, efficient code and memorable user experiences.<br><br>")
        .type("Currently, I'm exploring React to take my frontend skills to the next level. Whether it's a personal brand site, a creative portfolio, or an interactive animation — I enjoy turning ideas into reality on the web.<br><br>")
        .type("If you're looking for someone who cares about both aesthetics and functionality, I'm always open to collaboration and new opportunities!")
        .go();
      } catch (error) {
        console.error('TypeIt error:', error);
      }
    }

    window.addEventListener('scroll', startTyping);
    startTyping(); // Try immediately in case already in view

    debugLog('Typing effect initialized successfully');
    return true;

  } catch (error) {
    console.error('Failed to initialize typing effect:', error);
    return false;
  }
}

// Placeholder for chat system (keeping existing functionality)
function initializeChatSystem() {
  try {
    debugLog('Chat system using existing implementation');
    return true;
  } catch (error) {
    console.error('Failed to initialize chat system:', error);
    return false;
  }
}

// Enhanced mobile optimizations
function initializeMobileOptimizations() {
  try {
    debugLog('Initializing mobile optimizations');

    // Enhanced header scroll behavior
    if (elements.header) {
      let lastScrollTop = 0;

      function handleHeaderScroll() {
        if (window.innerWidth <= 700) {
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

          if (scrollTop > 100) {
            if (scrollTop > lastScrollTop) {
              elements.header.classList.add('scrolled');
              elements.header.classList.remove('scrolled-up');
            } else {
              elements.header.classList.remove('scrolled');
              elements.header.classList.add('scrolled-up');
            }
          } else {
            elements.header.classList.remove('scrolled');
            elements.header.classList.remove('scrolled-up');
          }

          lastScrollTop = scrollTop;
        }
      }

      window.addEventListener('scroll', handleHeaderScroll, { passive: true });
    }

    // Enhanced touch support for interactive elements
    const interactiveElements = safeQuerySelectorAll('.autoBlur, .autoDisplay, .fadeInRight, .poject-card, .social-icons a');

    interactiveElements.forEach(element => {
      element.style.touchAction = 'auto';
      element.style.pointerEvents = 'auto';

      element.addEventListener('touchstart', function() {
        debugLog('Touch detected on interactive element');
      }, { passive: true });
    });

    // Optimize viewport for mobile
    function optimizeViewport() {
      let viewport = safeQuerySelector('meta[name="viewport"]');
      if (!viewport) {
        viewport = document.createElement('meta');
        viewport.name = 'viewport';
        document.head.appendChild(viewport);
      }

      if (window.innerWidth <= 768) {
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover';
      } else {
        viewport.content = 'width=device-width, initial-scale=1.0';
      }
    }

    optimizeViewport();
    window.addEventListener('resize', optimizeViewport);

    debugLog('Mobile optimizations initialized successfully');
    return true;

  } catch (error) {
    console.error('Failed to initialize mobile optimizations:', error);
    return false;
  }
}

// Responsive video play/pause (hover for desktop, tap for mobile)
document.querySelectorAll('.pojects-vidbox video').forEach(video => {
  // Desktop hover
  video.addEventListener('mouseenter', () => {
    video.play();
    if (hoverSign) hoverSign.classList.add("active");
    gsap.to(video, { opacity: 1, duration: 0.5 });
  });

  video.addEventListener('mouseleave', () => {
    video.pause();
    video.currentTime = 0;
    if (hoverSign) hoverSign.classList.remove("active");
    gsap.to(video, { opacity: 0.6, duration: 0.5 });
  });

  // Mobile tap (touchend only)
  const toggleVideo = (e) => {
    // Add safety check for event
    if (e && e.preventDefault) {
      e.preventDefault();
    }

    if (video.paused) {
      video.play();
      if (hoverSign) hoverSign.classList.add("active");
      gsap.to(video, { opacity: 1, duration: 0.3 });
    } else {
      video.pause();
      video.currentTime = 0;
      if (hoverSign) hoverSign.classList.remove("active");
      gsap.to(video, { opacity: 0.6, duration: 0.3 });
    }
  };

  // Only add touchend for mobile, click for desktop
  video.addEventListener('click', toggleVideo);
  video.addEventListener('touchend', toggleVideo);
});

AOS.init({
  once: true, // animation happens only once as you scroll
  duration: 900 // animation duration in ms
});

document.addEventListener("DOMContentLoaded", function () {
  // Only trigger typing when About section is in view
  let typedStarted = false;
  function isInViewport(el) {
    const rect = el.getBoundingClientRect();
    return (
      rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.bottom >= 0
    );
  }
  function startTyping() {
    if (typedStarted) return;
    const aboutTyping = document.getElementById('about-typing');
    if (aboutTyping && isInViewport(aboutTyping)) {
      typedStarted = true;
      new TypeIt("#about-typing", {
        speed: 35,
        waitUntilVisible: true,
        cursor: true,
      })
      .type("Hi! I'm <strong>Shimul HP</strong>, a creative and detail-oriented frontend developer who builds interactive and visually engaging websites. I specialize in using HTML, CSS, JavaScript, and GSAP to bring static designs to life.<br><br>")
      .type("I'm passionate about creating user-friendly, performance-optimized websites that not only look great but also feel smooth and modern. From sleek landing pages to fully responsive portfolios, I focus on delivering clean, efficient code and memorable user experiences.<br><br>")
      .type("Currently, I'm exploring React to take my frontend skills to the next level. Whether it's a personal brand site, a creative portfolio, or an interactive animation — I enjoy turning ideas into reality on the web.<br><br>")
      .type("If you're looking for someone who cares about both aesthetics and functionality, I'm always open to collaboration and new opportunities!")
      .go();
    }
  }
  window.addEventListener('scroll', startTyping);
  startTyping();
});

// AI Chat Board Logic
/*
 * =============================================================
 * AI CHAT CONFIGURATION:
 *
 * The chat is now configured with a Gemini API key and is ready to use.
 * It uses the Gemini 2.0 Flash model to generate responses.
 *
 * If you need to change the API key in the future, update the API_KEY
 * constant below (around line 182).
 * =============================================================
 */
document.addEventListener("DOMContentLoaded", function () {
  // Handle fixed header on mobile
  const header = document.querySelector('header');
  let lastScrollTop = 0;

  // Function to handle header visibility on scroll
  function handleHeaderScroll() {
    if (window.innerWidth <= 700) { // Only apply on mobile
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      // If scrolled down more than 100px
      if (scrollTop > 100) {
        // Scrolling down
        if (scrollTop > lastScrollTop) {
          header.classList.add('scrolled');
          header.classList.remove('scrolled-up');
        }
        // Scrolling up
        else {
          header.classList.remove('scrolled');
          header.classList.add('scrolled-up');
        }
      } else {
        // At the top of the page
        header.classList.remove('scrolled');
        header.classList.remove('scrolled-up');
      }

      lastScrollTop = scrollTop;
    }
  }

  // Add scroll event listener
  window.addEventListener('scroll', handleHeaderScroll);

  // Fix for mobile touch events on animated elements
  const animatedElements = document.querySelectorAll('.autoBlur, .autoDisplay, .fadeInRight');
  const projectCards = document.querySelectorAll('.poject-card');
  const projectButtons = document.querySelectorAll('.pojects-info button');
  const socialIcons = document.querySelectorAll('.social-icons a');

  // Add touch event listeners to ensure clickability
  function addTouchSupport(elements) {
    elements.forEach(element => {
      element.style.touchAction = 'auto';
      element.style.pointerEvents = 'auto';

      // Add touch start listener to ensure element is responsive
      element.addEventListener('touchstart', function() {
        // Don't prevent default to allow normal touch behavior
        console.log('Touch detected on element');
      }, { passive: true });
    });
  }

  // Apply touch support to all animated elements
  addTouchSupport(animatedElements);
  addTouchSupport(projectCards);

  // Special handling for social icons
  socialIcons.forEach(icon => {
    // Ensure social icons are clickable
    icon.style.zIndex = '200';
    icon.style.position = 'relative';
    icon.style.pointerEvents = 'auto';
    icon.style.touchAction = 'manipulation';

    // Add specific touch event listeners
    icon.addEventListener('touchstart', function() {
      console.log('Social icon touched:', this.href);
      // Highlight the icon briefly to provide visual feedback
      this.style.transform = 'scale(1.2)';
      setTimeout(() => {
        this.style.transform = 'scale(1)';
      }, 150);
    }, { passive: true });

    // Ensure clicks work properly
    icon.addEventListener('click', function() {
      console.log('Social icon clicked:', this.href);
    });
  });

  // Ensure buttons are extra clickable on mobile
  projectButtons.forEach(button => {
    button.style.zIndex = '100';
    button.style.position = 'relative';
    button.style.pointerEvents = 'auto';

    // Make the touch target area larger
    button.addEventListener('touchstart', function() {
      console.log('Button touched');
    }, { passive: true });
  });

  // Contact Form Handling
  const contactForm = document.getElementById('contact-form');
  const formStatus = document.getElementById('form-status');

  if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();

      // Get form values
      const name = document.getElementById('name').value.trim();
      const email = document.getElementById('email').value.trim();
      const message = document.getElementById('message').value.trim();

      // Simple validation
      if (!name || !email || !message) {
        showFormStatus('error', 'Please fill in all fields');
        return;
      }

      // Email validation
      if (!isValidEmail(email)) {
        showFormStatus('error', 'Please enter a valid email address');
        return;
      }

      // Simulate form submission (replace with actual form submission)
      showFormStatus('success', 'Thank you for your message! I\'ll get back to you soon.');

      // Clear form
      contactForm.reset();

      // In a real implementation, you would send the form data to a server here
      console.log('Form submitted:', { name, email, message });

      // Example of how you would send the form data to a server:
      /*
      fetch('your-server-endpoint', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, email, message }),
      })
      .then(response => response.json())
      .then(data => {
        showFormStatus('success', 'Thank you for your message! I\'ll get back to you soon.');
        contactForm.reset();
      })
      .catch(error => {
        showFormStatus('error', 'There was an error sending your message. Please try again.');
        console.error('Error:', error);
      });
      */
    });
  }

  // Helper function to validate email
  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Helper function to show form status
  function showFormStatus(type, message) {
    if (formStatus) {
      formStatus.textContent = message;
      formStatus.className = type; // 'success' or 'error'
      formStatus.classList.remove('hidden');

      // Hide the status message after 5 seconds
      setTimeout(() => {
        formStatus.classList.add('hidden');
      }, 5000);
    }
  }

  // AI Chat Functionality (Simple Text Chat)
  const chatToggle = document.getElementById('ai-chat-toggle');
  const chatBoard = document.getElementById('ai-chat-board');
  const chatClose = document.getElementById('ai-chat-close');
  const chatForm = document.getElementById('ai-chat-form');
  const chatInput = document.getElementById('ai-chat-input');
  const chatMessages = document.getElementById('ai-chat-messages');

  // Voice Call Interface Elements
  const voiceCallInterface = document.getElementById('gemini-voice-call');
  const voiceCallClose = document.getElementById('voice-call-close');
  const startVoiceCallBtn = document.getElementById('start-voice-call');
  const muteVoiceCallBtn = document.getElementById('mute-voice-call');
  const endVoiceCallBtn = document.getElementById('end-voice-call');
  const callStatusText = document.getElementById('call-status-text');
  const callSubtitle = document.getElementById('call-subtitle');
  const voiceMessages = document.getElementById('voice-messages');

  // Voice Call Variables
  let voiceRecognition = null;
  let isVoiceCallActive = false;
  let isVoiceListening = false;
  let isMuted = false;
  let voiceSynthesis = window.speechSynthesis;

  // Check if elements exist to prevent errors
  if (!chatToggle || !chatBoard || !chatClose || !chatForm || !chatInput || !chatMessages) {
    console.error("One or more chat elements not found");
    return;
  }

  // Make sure toggle is visible and chat is hidden initially
  if (chatToggle) chatToggle.style.display = 'flex';
  if (chatBoard) chatBoard.style.display = 'none';

  // Initialize chat - show welcome message
  addMessage('AI', 'Hello! I\'m powered by Gemini AI. How can I help you today?');

  // Initialize Voice Call System
  function initVoiceCallSystem() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      voiceRecognition = new SpeechRecognition();

      voiceRecognition.continuous = true;
      voiceRecognition.interimResults = false;
      voiceRecognition.lang = 'en-US';

      voiceRecognition.onstart = function() {
        isVoiceListening = true;
        updateCallStatus('Listening...', 'Speak now, I\'m listening!');
        activateVoiceWaves();
        console.log('Voice call listening started');
      };

      voiceRecognition.onresult = function(event) {
        const transcript = event.results[event.results.length - 1][0].transcript;
        console.log('Voice input:', transcript);

        if (transcript.trim()) {
          addVoiceMessage('user', transcript);
          processVoiceInput(transcript);
        }
      };

      voiceRecognition.onerror = function(event) {
        console.error('Voice recognition error:', event.error);
        updateCallStatus('Error', 'Voice recognition error. Please try again.');
        deactivateVoiceWaves();

        if (event.error === 'not-allowed') {
          addVoiceMessage('system', 'Microphone access denied. Please allow microphone permissions.');
        }
      };

      voiceRecognition.onend = function() {
        if (isVoiceCallActive && !isMuted) {
          // Restart recognition if call is still active
          setTimeout(() => {
            if (isVoiceCallActive && !isMuted) {
              try {
                voiceRecognition.start();
              } catch (error) {
                console.error('Error restarting voice recognition:', error);
              }
            }
          }, 100);
        } else {
          isVoiceListening = false;
          deactivateVoiceWaves();
        }
      };

      console.log('Voice call system initialized');
    } else {
      console.log('Voice recognition not supported');
      addVoiceMessage('system', 'Voice recognition not supported in this browser.');
    }
  }

  // Voice Call Functions
  function updateCallStatus(status, subtitle) {
    if (callStatusText) callStatusText.textContent = status;
    if (callSubtitle) callSubtitle.textContent = subtitle;
  }

  function addVoiceMessage(type, message) {
    if (!voiceMessages) return;

    const messageDiv = document.createElement('div');
    messageDiv.className = `voice-message ${type}`;

    if (type === 'user') {
      messageDiv.innerHTML = `<strong>You:</strong> ${message}`;
    } else if (type === 'ai') {
      messageDiv.innerHTML = `<strong>Gemini:</strong> ${message}`;
    } else {
      messageDiv.innerHTML = `<em>${message}</em>`;
    }

    voiceMessages.appendChild(messageDiv);
    voiceMessages.scrollTop = voiceMessages.scrollHeight;
  }

  function activateVoiceWaves() {
    const voiceWaves = document.querySelector('.voice-waves');
    if (voiceWaves) {
      voiceWaves.classList.add('active');
    }
  }

  function deactivateVoiceWaves() {
    const voiceWaves = document.querySelector('.voice-waves');
    if (voiceWaves) {
      voiceWaves.classList.remove('active');
    }
  }

  async function processVoiceInput(input) {
    try {
      updateCallStatus('Processing...', 'Gemini is thinking...');

      // Call Gemini API
      const response = await askGemini(input);

      // Add AI response to voice messages
      addVoiceMessage('ai', response);

      // Speak the response
      speakVoiceResponse(response);

      updateCallStatus('Active Call', 'Continue speaking...');
    } catch (error) {
      console.error('Error processing voice input:', error);
      const errorMsg = 'Sorry, I encountered an error processing your request.';
      addVoiceMessage('ai', errorMsg);
      speakVoiceResponse(errorMsg);
      updateCallStatus('Active Call', 'Continue speaking...');
    }
  }

  function speakVoiceResponse(text) {
    if (voiceSynthesis && !isMuted) {
      voiceSynthesis.cancel();

      const cleanText = text.replace(/<[^>]*>/g, '').replace(/\*\*/g, '').replace(/\*/g, '');
      const utterance = new SpeechSynthesisUtterance(cleanText);

      utterance.rate = 0.9;
      utterance.pitch = 1;
      utterance.volume = 0.8;

      // Use a natural voice if available
      const voices = voiceSynthesis.getVoices();
      const preferredVoice = voices.find(voice =>
        voice.name.includes('Google') ||
        voice.name.includes('Microsoft') ||
        voice.name.includes('Natural')
      );

      if (preferredVoice) {
        utterance.voice = preferredVoice;
      }

      utterance.onstart = function() {
        updateCallStatus('Speaking...', 'Gemini is responding...');
        activateVoiceWaves();
      };

      utterance.onend = function() {
        updateCallStatus('Active Call', 'Continue speaking...');
        deactivateVoiceWaves();
      };

      voiceSynthesis.speak(utterance);
    }
  }

  // Initialize voice call system
  initVoiceCallSystem();

  // Voice Call Event Listeners
  if (startVoiceCallBtn) {
    startVoiceCallBtn.addEventListener('click', function() {
      if (!isVoiceCallActive) {
        startVoiceCall();
      } else {
        if (isMuted) {
          unmuteVoiceCall();
        } else {
          // If call is active and not muted, this acts as a push-to-talk
          if (!isVoiceListening) {
            startVoiceListening();
          }
        }
      }
    });
  }

  if (muteVoiceCallBtn) {
    muteVoiceCallBtn.addEventListener('click', function() {
      if (isVoiceCallActive) {
        if (isMuted) {
          unmuteVoiceCall();
        } else {
          muteVoiceCall();
        }
      }
    });
  }

  if (endVoiceCallBtn) {
    endVoiceCallBtn.addEventListener('click', function() {
      endVoiceCall();
    });
  }

  if (voiceCallClose) {
    voiceCallClose.addEventListener('click', function() {
      endVoiceCall();
    });
  }

  // Voice Call Control Functions
  function startVoiceCall() {
    isVoiceCallActive = true;
    isMuted = false;

    updateCallStatus('Starting Call...', 'Initializing voice connection...');

    // Update button states
    startVoiceCallBtn.classList.add('active');
    startVoiceCallBtn.innerHTML = '<i class="bx bx-microphone"></i><span>Listening</span>';
    muteVoiceCallBtn.classList.remove('active');

    // Add welcome message
    addVoiceMessage('ai', 'Hello! I\'m Gemini AI. I can hear you now. What would you like to talk about?');
    speakVoiceResponse('Hello! I\'m Gemini AI. I can hear you now. What would you like to talk about?');

    // Start voice recognition
    startVoiceListening();

    console.log('Voice call started');
  }

  function startVoiceListening() {
    if (voiceRecognition && !isVoiceListening && !isMuted) {
      try {
        voiceRecognition.start();
      } catch (error) {
        console.error('Error starting voice recognition:', error);
        addVoiceMessage('system', 'Could not start voice recognition. Please try again.');
      }
    }
  }

  function muteVoiceCall() {
    isMuted = true;

    // Stop voice recognition
    if (voiceRecognition && isVoiceListening) {
      voiceRecognition.stop();
    }

    // Stop any ongoing speech
    if (voiceSynthesis) {
      voiceSynthesis.cancel();
    }

    // Update UI
    muteVoiceCallBtn.classList.add('active');
    muteVoiceCallBtn.innerHTML = '<i class="bx bx-microphone-off"></i><span>Unmute</span>';
    startVoiceCallBtn.innerHTML = '<i class="bx bx-microphone-off"></i><span>Muted</span>';

    updateCallStatus('Call Muted', 'Click unmute to continue');
    deactivateVoiceWaves();

    console.log('Voice call muted');
  }

  function unmuteVoiceCall() {
    isMuted = false;

    // Update UI
    muteVoiceCallBtn.classList.remove('active');
    muteVoiceCallBtn.innerHTML = '<i class="bx bx-microphone-off"></i><span>Mute</span>';
    startVoiceCallBtn.innerHTML = '<i class="bx bx-microphone"></i><span>Listening</span>';

    updateCallStatus('Active Call', 'Continue speaking...');

    // Restart voice recognition
    startVoiceListening();

    console.log('Voice call unmuted');
  }

  function endVoiceCall() {
    isVoiceCallActive = false;
    isMuted = false;

    // Stop voice recognition
    if (voiceRecognition && isVoiceListening) {
      voiceRecognition.stop();
    }

    // Stop any ongoing speech
    if (voiceSynthesis) {
      voiceSynthesis.cancel();
    }

    // Reset UI
    startVoiceCallBtn.classList.remove('active');
    startVoiceCallBtn.innerHTML = '<i class="bx bx-microphone"></i><span>Start Call</span>';
    muteVoiceCallBtn.classList.remove('active');
    muteVoiceCallBtn.innerHTML = '<i class="bx bx-microphone-off"></i><span>Mute</span>';

    updateCallStatus('Call Ended', 'Click start to begin a new call');
    deactivateVoiceWaves();

    // Hide voice call interface
    if (voiceCallInterface) {
      voiceCallInterface.classList.add('hidden');
    }

    console.log('Voice call ended');
  }

  // Variable to track chat state
  let isChatOpen = false;

  // Check if we're on mobile
  const isMobile = () => window.innerWidth <= 768;

  // Toggle chat visibility
  chatToggle.onclick = function() {
    if (isMobile()) {
      // On mobile: toggle open/close with the same button
      if (isChatOpen) {
        // If already open, close it
        isChatOpen = false;
        chatBoard.style.display = 'none';
        chatToggle.classList.remove('active');
        console.log("Chat closed (mobile toggle)");
      } else {
        // If closed, open it
        isChatOpen = true;
        chatBoard.style.display = 'flex';
        console.log("Chat opened (mobile toggle)");
        // On mobile, add active class to indicate open state
        chatToggle.classList.add('active');
      }
    } else {
      // On desktop: original behavior
      isChatOpen = true;
      chatBoard.style.display = 'flex';
      chatToggle.style.display = 'none';
      console.log("Chat opened (desktop)");
    }
  };

  // Close button behavior
  chatClose.onclick = function() {
    isChatOpen = false;
    chatBoard.style.display = 'none';

    // On mobile, keep toggle visible but remove active class
    if (isMobile()) {
      chatToggle.classList.remove('active');
    } else {
      // On desktop, show the toggle button again
      chatToggle.style.display = 'flex';
    }

    console.log("Chat closed via X button");
  };

  // Handle window resize to ensure proper display on all devices
  window.addEventListener('resize', function() {
    // Check if we're switching between mobile and desktop
    const currentMobile = isMobile();

    // Always respect the current open/closed state
    if (isChatOpen) {
      chatBoard.style.display = 'flex';

      if (currentMobile) {
        // On mobile, toggle button is always visible with active class
        chatToggle.classList.add('active');
      } else {
        // On desktop, hide toggle button when chat is open
        chatToggle.style.display = 'none';
        chatToggle.classList.remove('active');
      }
    } else {
      // Chat is closed
      chatBoard.style.display = 'none';
      chatToggle.style.display = 'flex';
      chatToggle.classList.remove('active');
    }
  });

  function addMessage(sender, text) {
    const div = document.createElement('div');
    div.className = sender.toLowerCase() + '-message';
    div.innerHTML = `<strong>${sender}:</strong> ${text}`;
    chatMessages.appendChild(div);
    // After adding a new message:
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  // Fallback responses when API is not available
  const fallbackResponses = [
    "I'm a demo AI assistant. To enable real AI responses, please add your Gemini API key in the app.js file.",
    "This is a simulated response. For real AI interactions, you'll need to set up your API key.",
    "I'm currently in demo mode. To chat with the real AI, please configure your API key.",
    "Hello! I'm a placeholder assistant. The website owner needs to set up the API for me to give real responses.",
    "I can only provide pre-written responses until the API key is configured. Check the app.js file for instructions."
  ];

  // Check if API key is configured
  const API_KEY = "AIzaSyBIIg6QmcuC3qrI_YavYETZQMfYs3gFsvc"; // Your Gemini API key
  const isApiConfigured = true; // API key is now configured

  async function askGemini(question) {
    // If API key is not configured, return a fallback response
    if (!isApiConfigured) {
      console.log("API key not configured. Using fallback response.");
      return getRandomFallbackResponse();
    }

    try {
      console.log("Calling Gemini API with question:", question);

      // Simple responses for testing without API
      const simpleResponses = {
        "hello": "Hello! How can I help you today?",
        "hi": "Hi there! What can I do for you?",
        "how are you": "I'm just a program, but I'm functioning well! How can I assist you?",
        "what is your name": "I'm an AI assistant powered by Gemini. How can I help you today?",
        "who made you": "I was created by Google and integrated into this website by the developer."
      };

      // Check if we have a simple response for this question (case insensitive)
      const lowerQuestion = question.toLowerCase();
      for (const key in simpleResponses) {
        if (lowerQuestion.includes(key)) {
          console.log("Using simple response for:", key);
          return simpleResponses[key];
        }
      }

      // Proceed with API call
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contents: [{ parts: [{ text: question }] }]
        })
      });

      console.log("API response status:", response.status);

      if (!response.ok) {
        let errorInfo = "";
        try {
          const errorData = await response.json();
          errorInfo = JSON.stringify(errorData);
          console.error("API error details:", errorData);

          if (errorData.error && errorData.error.message) {
            if (errorData.error.message.includes("API key")) {
              return "API key error: The API key is invalid or has not been properly set up. Please check your configuration.";
            }
          }
        } catch (e) {
          errorInfo = await response.text();
        }

        console.error(`API error (${response.status}):`, errorInfo);

        if (response.status === 403 || response.status === 401) {
          return "API key error: The API key may be invalid or missing permissions. Please check your configuration.";
        }

        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      console.log("API response data:", data);

      if (data.candidates && data.candidates[0] && data.candidates[0].content &&
          data.candidates[0].content.parts && data.candidates[0].content.parts[0]) {
        return data.candidates[0].content.parts[0].text;
      } else {
        console.error("Unexpected API response format:", data);
        throw new Error("Unexpected API response format");
      }
    } catch (error) {
      console.error("Error calling Gemini API:", error);

      if (error.message.includes("Failed to fetch")) {
        return "Network error: Unable to connect to the AI service. Please check your internet connection.";
      }

      return "Sorry, I encountered an error. Please try again later. (Error: " + error.message + ")";
    }
  }

  // Get a random fallback response
  function getRandomFallbackResponse() {
    const randomIndex = Math.floor(Math.random() * fallbackResponses.length);
    return fallbackResponses[randomIndex];
  }

  chatForm.onsubmit = async function(e) {
    e.preventDefault();
    const msg = chatInput.value.trim();
    if (!msg) return;

    // Add user message
    addMessage('You', msg);
    chatInput.value = '';

    // Add AI thinking message
    addMessage('AI', '<em>Thinking...</em>');

    try {
      // Call Gemini API directly
      const reply = await askGemini(msg);

      // Replace thinking message with actual reply
      chatMessages.lastChild.innerHTML = `<strong>AI:</strong> ${reply}`;
    } catch (error) {
      console.error("Error in chat submission:", error);
      chatMessages.lastChild.innerHTML = `<strong>AI:</strong> Sorry, I encountered an error. Please try again.`;
    }
  };
});

// Improve mobile touch responsiveness for project cards
document.addEventListener("DOMContentLoaded", function() {
  // Fix for project cards on mobile
  const projectCards = document.querySelectorAll('.poject-card');

  projectCards.forEach(card => {
    // Make entire card area clickable on mobile
    if (window.innerWidth <= 768) { // Mobile breakpoint
      const videoBox = card.querySelector('.pojects-vidbox');
      const infoBox = card.querySelector('.pojects-info');
      const button = card.querySelector('.pojects-info button');

      // Ensure both sides are clickable
      [videoBox, infoBox].forEach(element => {
        if (element) {
          element.style.pointerEvents = 'auto';
          element.style.touchAction = 'auto';
          element.style.zIndex = '10';

          // Add touch feedback
          element.addEventListener('touchstart', function() {
            this.style.opacity = '0.9';
            setTimeout(() => {
              this.style.opacity = '1';
            }, 150);
          }, { passive: true });
        }
      });

      // Make sure button is extra clickable
      if (button) {
        button.style.zIndex = '100';
        button.style.position = 'relative';
      }
    }
  });

  // Downloads page functionality
  // Smooth scroll for the "Browse Downloads" button
  const browseButton = document.querySelector('.hero-info button');
  if (browseButton) {
    browseButton.addEventListener('click', function() {
      const softwareSection = document.getElementById('software');
      if (softwareSection) {
        softwareSection.scrollIntoView({ behavior: 'smooth' });
      }
    });
  }

  // Download cards hover effects and animations
  const downloadCards = document.querySelectorAll('.download-card');
  downloadCards.forEach(card => {
    // Add hover animation
    card.addEventListener('mouseenter', function() {
      gsap.to(this, { scale: 1.02, duration: 0.3, ease: 'power1.out' });
    });

    card.addEventListener('mouseleave', function() {
      gsap.to(this, { scale: 1, duration: 0.3, ease: 'power1.out' });
    });

    // Make download buttons more interactive
    const downloadButton = card.querySelector('.download-button');
    if (downloadButton) {
      downloadButton.addEventListener('mouseenter', function() {
        gsap.to(this, { scale: 1.05, duration: 0.2 });
      });

      downloadButton.addEventListener('mouseleave', function() {
        gsap.to(this, { scale: 1, duration: 0.2 });
      });

      // Handle actual downloads
      downloadButton.addEventListener('click', function(e) {
        const href = this.getAttribute('href');

        // Only prevent default for external links that should open in new tab
        if (href && (href.startsWith('http') && !href.includes('.mp3') && !href.includes('.zip') && !href.includes('.exe'))) {
          // For external websites, let them open normally
          return;
        }

        // For actual file downloads (mp3, zip, exe, etc.), enable download
        if (href && (href.includes('.mp3') || href.includes('.zip') || href.includes('.exe') || href.startsWith('./Audio/'))) {
          // Add download attribute for direct file downloads
          this.setAttribute('download', '');

          // Show download feedback
          const originalText = this.innerHTML;
          this.innerHTML = '<i class="bx bx-check"></i> Downloaded';

          // Restore original text after 2 seconds
          setTimeout(() => {
            this.innerHTML = originalText;
          }, 2000);

          return; // Allow the download to proceed
        }

        // For demo links (#), prevent default and show message
        if (!href || href === '#') {
          e.preventDefault();

          const originalText = this.innerHTML;
          this.innerHTML = '<i class="bx bx-info-circle"></i> Demo Link';

          setTimeout(() => {
            this.innerHTML = originalText;
          }, 2000);
        }
      });
    }

    // Mobile touch support for download cards
    if (window.innerWidth <= 768) {
      card.style.pointerEvents = 'auto';
      card.style.touchAction = 'auto';

      card.addEventListener('touchstart', function() {
        this.style.transform = 'scale(1.01)';
        setTimeout(() => {
          this.style.transform = 'scale(1)';
        }, 150);
      }, { passive: true });
    }
  });

  // Enhanced device detection for comprehensive responsiveness
  function getDeviceType() {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const userAgent = navigator.userAgent;
    const pixelRatio = window.devicePixelRatio || 1;

    // Device detection logic
    const devices = {
      // Large Desktop and 4K
      largeDesktop: width >= 1920,
      desktop: width >= 1200 && width < 1920,
      standardDesktop: width >= 1001 && width < 1200,

      // Tablets
      iPadPro: (width >= 1024 && height >= 1366) || (width >= 1366 && height >= 1024),
      largTablet: width >= 768 && width <= 1024,
      standardTablet: width >= 701 && width <= 767,

      // Mobile Phones
      iPhone14ProMax: (width >= 414 && width <= 430 && height >= 896) || userAgent.includes('iPhone'),
      iPhone13_14: width >= 390 && width <= 414 && height >= 844,
      iPhoneMini: width >= 360 && width <= 375 && height >= 812,
      galaxyS23Ultra: (width >= 400 && width <= 430 && height >= 850) || userAgent.includes('SM-S918'),

      // General categories
      mobile: width <= 768,
      tablet: width > 768 && width <= 1024,
      desktop: width > 1024,

      // High DPI displays
      highDPI: pixelRatio >= 2,

      // Orientation
      landscape: width > height,
      portrait: height > width
    };

    return devices;
  }

  // Apply device-specific optimizations
  function applyDeviceOptimizations() {
    const device = getDeviceType();
    const body = document.body;

    // Add device classes to body for CSS targeting
    if (device.iPhone14ProMax) body.classList.add('iphone-14-pro-max');
    if (device.iPhone13_14) body.classList.add('iphone-13-14');
    if (device.iPhoneMini) body.classList.add('iphone-mini');
    if (device.galaxyS23Ultra) body.classList.add('galaxy-s23-ultra');
    if (device.iPadPro) body.classList.add('ipad-pro');
    if (device.largTablet) body.classList.add('large-tablet');
    if (device.highDPI) body.classList.add('high-dpi');
    if (device.landscape) body.classList.add('landscape');
    if (device.portrait) body.classList.add('portrait');

    // Device-specific optimizations
    if (device.mobile) {
      // Mobile optimizations
      document.body.style.touchAction = 'manipulation';
      document.documentElement.style.scrollBehavior = 'smooth';

      // Optimize touch targets
      const interactiveElements = document.querySelectorAll('button, a, input, .download-button, .menu-icon, .close-icon');
      interactiveElements.forEach(element => {
        element.style.minHeight = '44px';
        element.style.minWidth = '44px';
        element.style.touchAction = 'manipulation';
      });
    }

    if (device.highDPI) {
      // High DPI optimizations
      const videos = document.querySelectorAll('video');
      videos.forEach(video => {
        video.style.willChange = 'transform';
        video.style.backfaceVisibility = 'hidden';
      });
    }

    if (device.tablet) {
      // Tablet-specific optimizations
      const sidebar = document.querySelector('.sidebar');
      if (sidebar && device.landscape) {
        sidebar.style.width = '350px';
      }
    }

    console.log('Device optimizations applied for:', Object.keys(device).filter(key => device[key]));
  }

  // Legacy function for backward compatibility
  function isGalaxyS23Ultra() {
    return getDeviceType().galaxyS23Ultra;
  }

  if (isGalaxyS23Ultra()) {
    // Enhanced touch responsiveness for Galaxy S23 Ultra
    document.body.style.touchAction = 'manipulation';

    // Optimize scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Enhanced button touch targets
    const buttons = document.querySelectorAll('button, .download-button, .pojects-info button');
    buttons.forEach(button => {
      button.style.minHeight = '48px';
      button.style.minWidth = '48px';
      button.style.touchAction = 'manipulation';
    });

    // Optimize video playback for Galaxy S23 Ultra
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
      video.style.willChange = 'transform';
      video.style.backfaceVisibility = 'hidden';
    });

    // Enhanced AI chat for Galaxy S23 Ultra
    const chatBoard = document.getElementById('ai-chat-board');
    const chatToggle = document.getElementById('ai-chat-toggle');

    if (chatBoard && chatToggle) {
      chatToggle.style.touchAction = 'manipulation';
      chatBoard.style.willChange = 'transform';

      // Optimize chat positioning for Galaxy S23 Ultra
      if (window.innerWidth <= 430) {
        chatBoard.style.borderRadius = '20px';
        chatToggle.style.borderRadius = '50%';
      }
    }

    // Optimize form inputs for Galaxy S23 Ultra
    const inputs = document.querySelectorAll('input, textarea');
    inputs.forEach(input => {
      input.style.fontSize = '16px'; // Prevents zoom on focus
      input.style.touchAction = 'manipulation';
    });

    console.log('Galaxy S23 Ultra optimizations applied');
  }

  // Apply comprehensive device optimizations
  applyDeviceOptimizations();

  // Re-apply optimizations on window resize
  let resizeTimeout;
  window.addEventListener('resize', function() {
    // Debounce resize events
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(applyDeviceOptimizations, 250);
  });

  // Optimize viewport meta tag for better mobile experience
  function optimizeViewport() {
    let viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport) {
      viewport = document.createElement('meta');
      viewport.name = 'viewport';
      document.head.appendChild(viewport);
    }

    const device = getDeviceType();
    if (device.mobile) {
      viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover';
    } else if (device.tablet) {
      viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=3.0, user-scalable=yes';
    } else {
      viewport.content = 'width=device-width, initial-scale=1.0';
    }
  }

  optimizeViewport();

  // Contact Form Handler
  const contactForm = document.getElementById('contact-form');
  const formStatus = document.getElementById('form-status');

  if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();

      // Get form data
      const formData = new FormData(contactForm);
      const name = formData.get('name');
      const email = formData.get('email');
      const message = formData.get('message');

      // Create mailto link with form data
      const subject = encodeURIComponent(`Contact from ${name} - Website Inquiry`);
      const body = encodeURIComponent(`
Name: ${name}
Email: ${email}

Message:
${message}

---
Sent from your website contact form
      `);

      // Create mailto URL with your email addresses
      const mailtoLink = `mailto:<EMAIL>,<EMAIL>?subject=${subject}&body=${body}`;

      // Show success message
      showFormStatus('Opening your email client...', 'success');

      // Open email client
      window.location.href = mailtoLink;

      // Reset form after a short delay
      setTimeout(() => {
        contactForm.reset();
        hideFormStatus();
      }, 2000);
    });
  }

  // Form status functions
  function showFormStatus(message, type) {
    if (formStatus) {
      formStatus.textContent = message;
      formStatus.className = `form-status ${type}`;
      formStatus.style.display = 'block';
    }
  }

  function hideFormStatus() {
    if (formStatus) {
      formStatus.style.display = 'none';
    }
  }
});

// Function to scroll to contact section from hero button
function scrollToContact() {
  const contactSection = document.getElementById('contact');
  if (contactSection) {
    contactSection.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  }
}

// Function to open Gemini Voice Call from the button
function openGeminiVoiceChat() {
  const voiceCallInterface = document.getElementById('gemini-voice-call');

  if (voiceCallInterface) {
    // Show the voice call interface
    voiceCallInterface.classList.remove('hidden');

    // Initialize the interface
    const callStatusText = document.getElementById('call-status-text');
    const callSubtitle = document.getElementById('call-subtitle');
    const voiceMessages = document.getElementById('voice-messages');

    if (callStatusText) callStatusText.textContent = 'Ready to start voice call';
    if (callSubtitle) callSubtitle.textContent = 'Click the microphone to begin speaking';

    // Clear previous messages
    if (voiceMessages) {
      voiceMessages.innerHTML = '';
    }

    // Add welcome message
    const welcomeDiv = document.createElement('div');
    welcomeDiv.className = 'voice-message ai';
    welcomeDiv.innerHTML = '<strong>Gemini:</strong> <em>🎤 Voice call interface ready! Click "Start Call" to begin your live conversation with Gemini AI.</em>';
    if (voiceMessages) {
      voiceMessages.appendChild(welcomeDiv);
    }

    console.log('Gemini Voice Call interface opened');
  }
}

// ========================================
// INITIALIZE EVERYTHING
// ========================================

// Multiple initialization strategies for maximum compatibility
if (document.readyState === 'loading') {
  // DOM is still loading
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  // DOM is already loaded
  initializeApp();
}

// Fallback initialization
window.addEventListener('load', function() {
  // Ensure everything is initialized even if DOMContentLoaded failed
  setTimeout(initializeApp, 100);
});

// Emergency fallback for hosting environments
setTimeout(function() {
  if (!window.appInitialized) {
    console.warn('⚠️ Emergency initialization triggered');
    initializeApp();
  }
}, 2000);

// Mark app as initialized
window.appInitialized = true;

console.log('🚀 Powerful App.js loaded successfully!');
