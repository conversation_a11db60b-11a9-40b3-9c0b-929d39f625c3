/* Downloads Page Specific Styles */

/* Active navigation link */
header ul a.active {
  color: #727fde;
  text-shadow: 0 0 10px #727fde;
}

/* Downloads Section */
.downloads-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60%;
  max-width: 1200px;
  margin: 150px auto 80px;
  padding: 40px 30px;
  box-sizing: border-box;
  background: rgba(51, 32, 80, 0.219);
  border-radius: 24px;
  border: 1px solid rgba(114, 127, 222, 0.3);
  box-shadow: 0 10px 30px rgba(43, 42, 42, 0.158),
              inset 0 1px 1px rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  top: -50px;
  overflow: hidden;
  gap: 20px; /* Added gap between child elements */
  /* Ensure perfect centering */
  left: 50%;
  transform: translateX(-50%);
}

.section-description {
  text-align: center;
  max-width: 700px;
  margin: 20px auto 40px;
  color: #eaeaea;
  font-size: 18px;
  line-height: 1.6;
}

.download-category {
  width: 100%;
  margin-bottom: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.category-title {
  font-size: 32px;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-title i {
  margin-left: 12px;
  font-size: 28px;
}

.download-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  width: 100%;
  max-width: 1100px;
  justify-items: center;
  justify-content: center;
  margin: 0 auto;
}

/* Download Card Styles */
.download-card {
  display: flex;
  background: rgba(34, 0, 73, 0.15);
  border-radius: 18px;
  box-shadow: 0 4px 24px #727fde33;
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  border: 1px solid rgba(114, 127, 222, 0.2);
  padding: 25px;
  width: 100%;
  max-width: 500px;
  box-sizing: border-box;
  margin: 0 auto;
}

.download-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 32px #727fde55;
}

.download-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #2200493d, #727fde33);
  border-radius: 16px;
  margin-right: 20px;
  flex-shrink: 0;
}

.download-icon i {
  font-size: 40px;
  color: #727fde;
}

.music-icon {
  background: linear-gradient(135deg, #2200493d, #00bcd433);
}

.music-icon i {
  color: #00bcd4;
}

.windows-icon {
  background: linear-gradient(135deg, #2200493d, #0078d733);
}

.windows-icon i {
  color: #0078d7;
}

.linux-icon {
  background: linear-gradient(135deg, #2200493d, #e95420aa);
}

.linux-icon i {
  color: #e95420;
}

.download-info {
  flex: 1;
}

.download-info h2 {
  font-size: 22px;
  margin-bottom: 10px;
  color: #fff;
}

.download-info p {
  font-size: 15px;
  color: #eaeaea;
  margin-bottom: 15px;
  line-height: 1.5;
}

.download-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
  font-size: 14px;
  color: #b0b0b0;
}

.download-meta span {
  display: flex;
  align-items: center;
}

.download-meta i {
  margin-right: 5px;
  font-size: 16px;
  color: #727fde;
}

.download-button {
  display: inline-flex;
  align-items: center;
  padding: 10px 20px;
  background: #2200493d;
  color: #fff;
  border: 1px solid #727fdeb4;
  border-radius: 12px;
  text-decoration: none;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 0 5px #727fde86;
}

.download-button i {
  margin-right: 8px;
  font-size: 18px;
}

.download-button:hover {
  background: rgba(114, 127, 222, 0.3);
  box-shadow: 0 0 15px #727fde6f;
}

/* Audio Player Styles */
.audio-player {
  width: 100%;
  margin: 15px 0;
}

.audio-player audio {
  width: 100%;
  height: 40px;
  border-radius: 8px;
  background: rgba(34, 0, 73, 0.3);
}

/* Custom audio controls */
audio::-webkit-media-controls-panel {
  background: rgba(34, 0, 73, 0.5);
}

audio::-webkit-media-controls-play-button,
audio::-webkit-media-controls-volume-slider,
audio::-webkit-media-controls-mute-button {
  filter: invert(100%);
}

/* Add vertical gap between .list items inside .downloads-section */
.downloads-section .list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Responsive Styles */
@media screen and (max-width: 1000px) {
  .downloads-section {
    width: 90%;
    left: 50%;
    transform: translateX(-50%);
  }

  .download-grid {
    grid-template-columns: 1fr;
  }
}

@media screen and (max-width: 768px) {
  .downloads-section {
    width: 95%;
    padding: 20px 10px;
    left: 50%;
    transform: translateX(-50%);
  }

  .section-description {
    font-size: 16px;
    padding: 0 15px;
  }

  .download-category {
    padding: 0 10px;
  }

  .download-grid {
    padding: 0 5px;
  }

  .download-card {
    flex-direction: column;
    padding: 20px;
    max-width: 90%;
  }

  .download-icon {
    margin: 0 auto 20px;
  }

  .download-info h2 {
    text-align: center;
  }

  .download-meta {
    justify-content: center;
  }

  .download-button {
    display: flex;
    justify-content: center;
    width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .section-title {
    font-size: 28px;
    text-align: center;
    padding: 0 10px;
  }

  .section-description {
    font-size: 14px;
    padding: 0 15px;
  }

  .category-title {
    font-size: 26px;
    text-align: center;
    flex-wrap: wrap;
    justify-content: center;
  }

  .category-title i {
    font-size: 22px;
    margin-top: 5px;
  }

  .download-card {
    max-width: 100%;
    padding: 15px;
  }

  .download-icon {
    width: 70px;
    height: 70px;
  }

  .download-icon i {
    font-size: 32px;
  }

  .download-info h2 {
    font-size: 20px;
    text-align: center;
  }

  .download-info p {
    font-size: 14px;
    text-align: center;
  }

  .download-meta {
    font-size: 12px;
    gap: 10px;
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }

  .download-button {
    font-size: 14px;
    padding: 8px 16px;
    width: 100%;
    justify-content: center;
  }
}

@media screen and (max-width: 360px) {
  .downloads-section {
    width: 100%;
    padding: 20px 5px;
    left: 50%;
    transform: translateX(-50%);
  }

  .download-card {
    padding: 12px;
  }

  .download-icon {
    width: 60px;
    height: 60px;
  }

  .download-icon i {
    font-size: 28px;
  }

  .download-info h2 {
    font-size: 18px;
  }

  .download-info p {
    font-size: 13px;
  }

  .download-meta span {
    font-size: 11px;
  }

  .download-button {
    font-size: 13px;
    padding: 8px 12px;
  }
}

/* Samsung Galaxy S23 Ultra Specific Optimizations */
@media screen and (max-width: 430px) and (min-width: 400px) and (min-height: 850px) {
  .downloads-section {
    width: 96%;
    padding: 30px 20px;
    margin: 120px auto 60px;
    left: 50%;
    transform: translateX(-50%);
  }

  .section-title {
    font-size: 32px;
    margin-bottom: 20px;
  }

  .section-description {
    font-size: 16px;
    max-width: 90%;
    margin: 15px auto 35px;
    padding: 0 10px;
  }

  .category-title {
    font-size: 28px;
    margin-bottom: 20px;
    text-align: center;
  }

  .category-title i {
    font-size: 24px;
    margin-left: 10px;
  }

  .download-grid {
    gap: 25px;
    padding: 0 10px;
  }

  .download-card {
    padding: 20px;
    max-width: 100%;
    margin: 0 auto;
    border-radius: 16px;
  }

  .download-icon {
    width: 75px;
    height: 75px;
    margin: 0 auto 15px;
  }

  .download-icon i {
    font-size: 36px;
  }

  .download-info h2 {
    font-size: 20px;
    text-align: center;
    margin-bottom: 12px;
  }

  .download-info p {
    font-size: 15px;
    text-align: center;
    line-height: 1.5;
    margin-bottom: 18px;
  }

  .download-meta {
    font-size: 13px;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 18px;
  }

  .download-meta span {
    background: rgba(114, 127, 222, 0.1);
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 12px;
  }

  .download-button {
    font-size: 15px;
    padding: 12px 20px;
    width: 100%;
    justify-content: center;
    border-radius: 14px;
  }

  .audio-player {
    margin: 18px 0;
  }

  .audio-player audio {
    height: 45px;
    border-radius: 10px;
  }
}

/* Galaxy S23 Ultra Landscape Mode */
@media screen and (min-width: 850px) and (max-width: 950px) and (max-height: 430px) {
  .downloads-section {
    width: 85%;
    padding: 20px 25px;
    margin: 80px auto 40px;
  }

  .download-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .download-card {
    flex-direction: row;
    padding: 15px;
    max-width: none;
  }

  .download-icon {
    margin: 0 15px 0 0;
    width: 60px;
    height: 60px;
  }

  .download-info h2 {
    text-align: left;
    font-size: 18px;
  }

  .download-info p {
    text-align: left;
    font-size: 14px;
  }

  .download-meta {
    justify-content: flex-start;
  }

  .download-button {
    width: auto;
    padding: 8px 16px;
  }
}
