/* ========================================
   BROWSER COMPATIBILITY FIXES
   Ensures your website looks identical to Brave browser
   ======================================== */

/* Force all browsers to use the same rendering engine behavior */
* {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    text-rendering: optimizeLegibility !important;
}

/* Cross-browser flexbox fixes */
.hero-buttons, .box-icon, header ul, header, .container, #main {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -moz-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
}

/* Cross-browser transform fixes */
.hero-info {
    -webkit-transform: translateY(-50%) !important;
    -moz-transform: translateY(-50%) !important;
    -ms-transform: translateY(-50%) !important;
    -o-transform: translateY(-50%) !important;
    transform: translateY(-50%) !important;
}

/* Cross-browser backdrop-filter fixes */
header, .sidebar {
    -webkit-backdrop-filter: blur(10px) !important;
    -moz-backdrop-filter: blur(10px) !important;
    -ms-backdrop-filter: blur(10px) !important;
    backdrop-filter: blur(10px) !important;
}

/* Cross-browser border-radius fixes */
header ul, .hero-info-title, .hero-cta-btn, .hero-whatsapp-btn, .box-icon a {
    -webkit-border-radius: inherit !important;
    -moz-border-radius: inherit !important;
    border-radius: inherit !important;
}

/* Cross-browser box-shadow fixes */
header, .hero-cta-btn, .hero-whatsapp-btn, .box-icon a {
    -webkit-box-shadow: inherit !important;
    -moz-box-shadow: inherit !important;
    box-shadow: inherit !important;
}

/* Cross-browser transition fixes */
header ul a, .hero-cta-btn, .hero-whatsapp-btn, .box-icon a {
    -webkit-transition: all 0.3s ease !important;
    -moz-transition: all 0.3s ease !important;
    -ms-transition: all 0.3s ease !important;
    -o-transition: all 0.3s ease !important;
    transition: all 0.3s ease !important;
}

/* Force hardware acceleration for smooth animations */
.hero, .sidebar, .download-card, .poject-card, header, .hero-cta-btn, .hero-whatsapp-btn {
    -webkit-transform: translateZ(0) !important;
    -moz-transform: translateZ(0) !important;
    -ms-transform: translateZ(0) !important;
    -o-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    -webkit-backface-visibility: hidden !important;
    -moz-backface-visibility: hidden !important;
    -ms-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
}

/* Cross-browser gradient fixes */
.hero-cta-btn, .hero-whatsapp-btn {
    background: -webkit-linear-gradient(135deg, #727fde, #8b5cf6) !important;
    background: -moz-linear-gradient(135deg, #727fde, #8b5cf6) !important;
    background: -ms-linear-gradient(135deg, #727fde, #8b5cf6) !important;
    background: -o-linear-gradient(135deg, #727fde, #8b5cf6) !important;
    background: linear-gradient(135deg, #727fde, #8b5cf6) !important;
}

.hero-whatsapp-btn {
    background: -webkit-linear-gradient(135deg, #25d366, #128c7e) !important;
    background: -moz-linear-gradient(135deg, #25d366, #128c7e) !important;
    background: -ms-linear-gradient(135deg, #25d366, #128c7e) !important;
    background: -o-linear-gradient(135deg, #25d366, #128c7e) !important;
    background: linear-gradient(135deg, #25d366, #128c7e) !important;
}

/* Cross-browser video fixes */
video {
    -webkit-object-fit: cover !important;
    -moz-object-fit: cover !important;
    -ms-object-fit: cover !important;
    -o-object-fit: cover !important;
    object-fit: cover !important;
}

/* Cross-browser scroll behavior */
html {
    -webkit-scroll-behavior: smooth !important;
    -moz-scroll-behavior: smooth !important;
    -ms-scroll-behavior: smooth !important;
    scroll-behavior: smooth !important;
}

/* Browser-specific fixes for mobile */
@media screen and (max-width: 768px) {
    /* iOS Safari fixes */
    body {
        -webkit-overflow-scrolling: touch !important;
        -webkit-text-size-adjust: 100% !important;
    }
    
    /* Android Chrome fixes */
    input, textarea, select {
        -webkit-appearance: none !important;
        -moz-appearance: none !important;
        appearance: none !important;
    }
    
    /* Mobile button fixes */
    .hero-cta-btn, .hero-whatsapp-btn, button {
        -webkit-tap-highlight-color: transparent !important;
        -webkit-touch-callout: none !important;
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
    }
}

/* Force consistent font rendering */
@font-face {
    font-family: 'Arial';
    src: local('Arial'), local('Arial-Regular');
    font-display: swap;
}

/* Browser-specific CSS hacks */
/* Internet Explorer */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .hero-buttons {
        display: block !important;
    }
    .hero-cta-btn, .hero-whatsapp-btn {
        display: inline-block !important;
        margin-bottom: 10px !important;
    }
}

/* Firefox */
@-moz-document url-prefix() {
    .hero-info {
        transform: translateY(-50%) !important;
    }
}

/* Safari */
@media not all and (min-resolution:.001dpcm) {
    @supports (-webkit-appearance:none) {
        .hero-buttons {
            display: -webkit-flex !important;
        }
    }
}

/* Edge */
@supports (-ms-ime-align:auto) {
    .hero-buttons {
        display: -ms-flexbox !important;
    }
}

/* Chrome and Opera */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .hero-buttons {
        display: -webkit-flex !important;
    }
}

/* Force consistent appearance across all browsers */
body {
    font-family: Arial, sans-serif !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* Ensure buttons work consistently */
button, .hero-cta-btn, .hero-whatsapp-btn, .download-button {
    cursor: pointer !important;
    border: none !important;
    outline: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

/* Cross-browser flexbox alignment */
.hero-buttons {
    -webkit-box-align: center !important;
    -webkit-align-items: center !important;
    -moz-align-items: center !important;
    -ms-flex-align: center !important;
    align-items: center !important;
    
    -webkit-box-pack: start !important;
    -webkit-justify-content: flex-start !important;
    -moz-justify-content: flex-start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
}

/* Ensure consistent spacing */
.hero-buttons > * {
    margin-right: 15px !important;
}

.hero-buttons > *:last-child {
    margin-right: 0 !important;
}
