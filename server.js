// Load environment variables
require('dotenv').config();

const express = require('express');
const fetch = require('node-fetch');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = process.env.PORT || 3001;
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

// Debug environment variables
console.log('Environment variables loaded:', {
  PORT: process.env.PORT,
  GEMINI_API_KEY: GEMINI_API_KEY ? 'DEFINED' : 'UNDEFINED'
});

// Ensure API key is present
if (!GEMINI_API_KEY) {
  console.error('ERROR: GEMINI_API_KEY is not defined in environment variables');
  process.exit(1);
}

// Middleware
app.use(cors());
app.use(bodyParser.json());

// POST route to communicate with Gemini API
app.post('/api/gemini', async (req, res) => {
  const userMessage = req.body.message;

  if (!userMessage) {
    return res.status(400).json({ reply: 'Message is required.' });
  }

  const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;

  console.log('Using API URL:', apiUrl.replace(GEMINI_API_KEY, 'REDACTED'));

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        contents: [{ parts: [{ text: userMessage }] }]
      })
    });

    const data = await response.json();

    const aiText = data?.candidates?.[0]?.content?.parts?.[0]?.text || "Sorry, I couldn't get a response.";
    res.json({ reply: aiText });
  } catch (err) {
    console.error('Gemini API error:', err);
    res.status(500).json({ reply: "Error connecting to Gemini API." });
  }
});

// Start server
app.listen(PORT, () => console.log(`✅ Gemini backend running on port ${PORT}`));
