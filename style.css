/* ========================================
   GLOBAL RESET AND BASE STYLES
   ======================================== */

/* Universal Reset - Removes default margins, padding, and sets consistent box-sizing */
*{
    margin: 0;
    padding: 0;
    font-family: "arial", sans-serif;
    box-sizing: border-box;
}


/* HTML and Body Base Styles - Sets full viewport dimensions and scroll behavior */
html,body{
    overflow-x: visible;
    width: 100%;
    height: 100%;
}

/* Smooth Scrolling - Enables smooth scroll transitions for anchor links */
html {
    scroll-behavior: smooth;
}

/* ========================================
   MAIN LAYOUT CONTAINERS
   ======================================== */

/* Main Container - Primary wrapper for entire page content */
#main{
    width: 100%;
    min-height: 100vh;
    position: relative;
    color: lightgray;
    display: flex;
}

/* Content Container - Main content wrapper with vertical layout */
.container{
    position: relative;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 100px;
    background-color: #001f7c38;
}

/* ========================================
   BACKGROUND ELEMENTS
   ======================================== */

/* Background Videos - Fixed background video elements with blend mode */
.back-videos{
    position: fixed;
    right: 0;
    bottom: 0;
    z-index: -1;
    mix-blend-mode: lighten;
}

/* ========================================
   HEADER AND NAVIGATION
   ======================================== */

/* Main Header - Fixed navigation bar with glassmorphism effect */
header{
    display: flex;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    background-color: rgba(255, 255, 255, 0.123,);
    backdrop-filter: blur(10px);
    padding: 0 30px;
    box-shadow: 0 0 15px #7ea9e0bb;
    z-index: 999;
}



/* Header Left Section - Logo and branding area */
.left{
    display: flex;
    align-items: center;
}

/* Header Logo Image - Profile/brand image styling */
.left img{
    width: 60px;
    margin: 0 20px;
    border-radius: 80px;
}

/* Navigation Menu Container - Main navigation list with glassmorphism */
header ul{
    display: flex;
    justify-content: space-between;
    padding: 15px 40px;
    border-radius: 50px;
    background-color: rgba(0, 0, 69, 0.692);
    backdrop-filter: blur(10px);
    box-shadow: 0 0 10px #e3e4e93f;
    position: relative;
    overflow: hidden;
}

/* Navigation Shimmer Effect - Animated light sweep on hover */
header ul::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

/* Navigation Shimmer Trigger - Activates shimmer animation on hover */
header ul:hover::before {
    left: 100%;
}

/* Navigation List Items - Individual menu items */
header ul li{
    list-style: none;
    position: relative;
}

/* Navigation Links - Styled navigation menu links with animations */
header ul a{
    text-decoration: none;
    color: white;
    font-weight: 700;
    margin: 0 10px;
    padding: 12px 20px;
    border-radius: 25px;
    position: relative;
    display: inline-block;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    z-index: 1;
}

/* Navigation Link Background Effect - Radial gradient background on hover */
header ul a::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(114, 127, 222, 0.8) 0%, rgba(139, 92, 246, 0.6) 100%);
    border-radius: 50%;
    transition: all 0.5s ease;
    transform: translate(-50%, -50%);
    z-index: -1;
}

/* Navigation Link Underline Effect - Animated bottom border */
header ul a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #727fde, #8b5cf6, #a855f7);
    transition: all 0.4s ease;
    transform: translateX(-50%);
}

header ul a:hover {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 8px 25px rgba(114, 127, 222, 0.3),
        0 3px 10px rgba(114, 127, 222, 0.2);
}

header ul a:hover::before {
    width: 120%;
    height: 120%;
}

header ul a:hover::after {
    width: 100%;
}

header ul a:active {
    transform: translateY(-1px) scale(1.02);
    transition: all 0.1s ease;
}

/* Glowing effect for active/current page */
header ul a.active {
    background: linear-gradient(135deg, rgba(114, 127, 222, 0.3), rgba(139, 92, 246, 0.3));
    color: #ffffff;
    box-shadow:
        0 0 20px rgba(114, 127, 222, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Ripple effect on click */
@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

header ul a:active::before {
    animation: ripple 0.6s ease-out;
}

/* Floating animation for navbar */
@keyframes navFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-2px); }
}

header ul:hover {
    animation: navFloat 2s ease-in-out infinite;
    box-shadow:
        0 5px 25px rgba(114, 127, 222, 0.2),
        0 0 15px #e3e4e93f;
}

/* ========================================
   SOCIAL MEDIA ICONS (HEADER)
   ======================================== */

/* Social Icons Container - Header social media icons wrapper */
.box-icon{
    display: flex;
    gap: 35px;
}

/* Social Icon Links - Individual social media icon styling with glassmorphism */
.box-icon a{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 25px;
    width: 45px;
    height: 45px;
    border: 2px solid #727fde;
    text-decoration: none;
    color: lightgray;
    border-radius: 50%;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    background: rgba(114, 127, 222, 0.05);
    backdrop-filter: blur(10px);
}

.box-icon a::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(114, 127, 222, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transition: all 0.5s ease;
    transform: translate(-50%, -50%);
    z-index: -1;
}

.box-icon a::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: rotate(-45deg) translateX(-100%);
    transition: transform 0.6s ease;
    z-index: 1;
}

.box-icon a:hover{
    background: linear-gradient(135deg, #727fde, #8b5cf6);
    color: white;
    transform: translateY(-8px) scale(1.15) rotate(5deg);
    box-shadow:
        0 15px 35px rgba(114, 127, 222, 0.4),
        0 5px 15px rgba(114, 127, 222, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.box-icon a:hover::before {
    width: 120%;
    height: 120%;
}

.box-icon a:hover::after {
    transform: rotate(-45deg) translateX(100%);
}

.box-icon a:active {
    transform: translateY(-4px) scale(1.05);
    transition: all 0.1s ease;
}

/* Platform-specific hover colors */
.box-icon a[href*="github"]:hover {
    background: linear-gradient(135deg, #333, #24292e);
    box-shadow:
        0 15px 35px rgba(51, 51, 51, 0.4),
        0 5px 15px rgba(51, 51, 51, 0.2);
}

.box-icon a[href*="git-scm"]:hover {
    background: linear-gradient(135deg, #f05032, #e94e2d);
    box-shadow:
        0 15px 35px rgba(240, 80, 50, 0.4),
        0 5px 15px rgba(240, 80, 50, 0.2);
}

.box-icon a[href*="visualstudio"]:hover {
    background: linear-gradient(135deg, #007acc, #005a9e);
    box-shadow:
        0 15px 35px rgba(0, 122, 204, 0.4),
        0 5px 15px rgba(0, 122, 204, 0.2);
}

/* Pulse animation on hover */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.box-icon a:hover {
    animation: pulse 2s infinite;
}

/* Floating effect */
@keyframes float {
    0%, 100% { transform: translateY(-8px) scale(1.15) rotate(5deg); }
    50% { transform: translateY(-12px) scale(1.15) rotate(5deg); }
}

.box-icon a:hover {
    animation: float 3s ease-in-out infinite;
}

/* ========================================
   HERO SECTION
   ======================================== */

/* Blackhole Background Effect - Animated background video container */
.blackhole-box{
    z-index: -1;
    position: absolute;
    top: 0;
    width: 100%;
    justify-content: center;
    mix-blend-mode: lighten;
}

/* Blackhole Video - Background animation video */
.blackhole-box video{
    width: 100%;
    margin-top: -23.5%;
}

/* Hero Section Container - Main landing section layout */
.hero{
    position: relative;
    display: flex;
    width: 100%;
    height: 100vh;
    align-items: center;
    justify-content: space-between;
}


/* Hero Information Container - Left side content area */
.hero-info{
    position: absolute;
    left: 5%;
}

/* Hero Title Badge - Styled title/tagline container */
.hero-info-title{
    color: #727fde;
    padding: 8px 5px;
    border: 1px solid #727fde77;
    width: 240px;
    background-color: #2200493d;
    box-shadow:  0 0 5px #727fde88;
    border-radius: 50px;
}

/* Hero Main Heading - Primary hero title */
.hero-info h1{
    font-size: 60px;
    max-width: 600px;
    font-weight: 700;
    line-height: 70px;
    margin-top: 40px;
    margin-bottom: 30px;
}

/* Hero Description - Hero section paragraph text */
.hero-info p{
    max-width: 550px;
    line-height: 25px;
    margin-bottom: 40px;
    font-size: 20px;
}

/* Hero Call-to-Action Button - Primary action button */
.hero-info button{
    color: white;
    padding: 15px 35px;
    border-radius: 15px;
    border: 1px solid #727fdeb4;
    background-color: #2200493d;
    box-shadow:  0 0 5px #727fde86;
    cursor: pointer;
    transition: 0.3s;
}


.hero-info button:hover{
    box-shadow: 0 0 30px #727fde6f;
}


.gradient{
    background: linear-gradient(to right, #008baa, #7e42a7, #6600c5, #6070fd, #2a46ff, #0099ff);
    background-size: 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: animate-gradient 2.5s linear infinite;
}



@keyframes animate-gradient {
    to{
        background-position: 200%;
    }
}


.hero-video-box{
    position: absolute;
    right: 0%;
}


.hero-video-box video{
    height: 800px;
    mix-blend-mode: lighten;
}


.scroll-down{
    height: 50px;
    width: 30px;
    border: 2px solid lightgray;
    position: absolute;
    left: 49%;
    bottom: 8%;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: 0 0 15px rgba(211, 211, 211, 0.477);
}



.scroll-down::before,
.scroll-down::after {
    content:"";
    position: absolute;
    top: 20%;
    left: 49%;
    height: 10px;
    border: 2px solid lightgray;
    transform: translate(-50%, 100%) rotate(45deg);
    border-top: transparent;
    border-left: transparent;
    animation: scroll-down 2s ease-in-out infinite;
}



.scroll-down::before,
.scroll-down::after {
  content: "";
  position: absolute;
  left: 50%;
  height: 10px;
  width: 10px;
  border: 2px solid lightgray;
  transform: translate(-50%, -100%) rotate(45deg);
  border-top: transparent;
  border-left: transparent;
  animation: scroll-down 2s ease-in-out infinite;
}


.scroll-down::before {
  top: 20%;
    animation-delay: 0.5s;
}

.scroll-down::after {
    top: 30%;
    animation-delay: 1s;
  }

  @keyframes scroll-down {
    0% {
      opacity: 0;
    }
    30%, 60% {
      opacity: 1;
    }
    100% {
      top: 90%;
      opacity: 0;
    }
  }




  .info-section{
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 80%;
    margin-top: 100px;
  }

  .section-title{
    font-size: 40px;
    font-weight: 700;
  }

  .info-card{
    display: grid;
    grid-template-columns: auto auto auto;
    gap: 20px;
    width: 100%;
    height: 100%;
    margin-top: 30px;
  }



  .card{
    display: flex;
    align-items: flex-start;
    justify-content: center;
    position: relative;
    width: auto;
    height: 40vh;
    overflow: hidden;
    border: 1px solid gray;
    background-color: #080020b7;
    border-radius: 20px;
    transition: 0.3s;
  }


  .card img{
    width: 80%;
    height: 50%;
    object-fit: cover;
  }

  .card h1{
    position: absolute;
    margin: 0;
    bottom: 40%;
    left: 5%;
    font-size: 25px;
    z-index: 1;
    color: lightgray;
  }


    .card p{
        position: absolute;
        bottom: 1%;
        left: 5%;
        z-index: 1;
        max-width: 300px;
        color: gray;
        font-size: 13px;
        line-height: 18px;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }

    .card video{
        margin-top: 10%;
        height: 70%;
        object-fit: cover;
        mix-blend-mode: lighten;
    }


    .card button{
    color: white;
    padding: 10px 15px;
    border-radius: 15px;
    border: 1px solid #727fdeb4;
    background-color: #2200493d;
    box-shadow:  0 0 5px #727fde86;
    cursor: pointer;
    transition: 0.3s;
    position: absolute;
    bottom: 2%;
    left: 5%;
    }


    .card button:hover{
        box-shadow: 0 0 20px #727fde6f;
        opacity: 0.7;
    }


    .card:hover{
        box-shadow: 0 0 15px #9aa0d38f;
    }


    .card:nth-child(3){
       grid-row: span 2;
       height: 83vh;
    }

    .card:nth-child(3) P{
        bottom: 12%;
    }


    .card:nth-child(3) h1{
        bottom: 21%;
    }


    .card:nth-child(4){
        grid-column: span 2;
     }


     .card:nth-child(4) P{
        max-width: 650px;
    }


    .card:nth-child(4) h1{
        bottom: 35%;
    }


    .my-pojects{
        display: flex;
        flex-direction: column;
        gap: 10%;
        align-items: center;
        position: relative;
        width: 80%;
        height: 100vh;
        margin-top: 200px;
        margin-bottom: 700px;
    }

    .poject-card{
        display: flex;
        width: 80%;
        height: 40%;
        align-items: center;
        gap: 10%;
        justify-content: center;
    }


    .pojects-vidbox{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50%;
        position: relative;
        cursor: pointer;
        min-width: 400px;
        transition: 0.5s;
        mix-blend-mode: exclusion;
    }

    .pojects-vidbox video{
        object-fit: cover;
        width: 100%;
        box-shadow: 0 0 10px lightgray;
        border-radius: 20px;
        transition: 0.5s;
    }

    .pojects-vidbox video:hover{
        box-shadow: 0 0 20px lightgray;
    }


    .pojects-info{
        display: flex;
        flex-direction: column;
        align-items: start;
        justify-content: center;
        width: 50%;
        padding-left: 10%;
    }



    .pojects-info h1 {
        font-size: 25px;
        font-weight: bold;
        max-width: 450px;
        margin-bottom: 10px;
        margin-top: 0;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }


    .pojects-info p {
        font-size: 16px;
        max-width: 400px;
        line-height: 1.5;
        margin-bottom: 50px;
        margin-top: 0;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }

    .pojects-info button{
        color: white;
        padding: 10px 15px;
        border-radius: 15px;
        border: 1px solid #727fdeb4;
        background-color: #2200493d;
        box-shadow:  0 0 5px #727fde86;
        cursor: pointer;
        transition: 0.3s;
    }


    .pojects-info button:hover{
        box-shadow: 0 0 20px #727fde6f;
        opacity: 0.7;
    }



    .hover-sign{
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30%;
        height: 100px;
    }

    .hover-sign::before,
    .hover-sign::after{
        content: "";
        position: absolute;
        font-size: 50px;
        top: 20%;
        left: 40%;
        border-radius: 50%;
        animation: hoverAnimation 3s ease-in-out infinite;
    }



    @keyframes hoverAnimation {
        0%{
            box-shadow: 0 0 5px lightgray;
            transform: translate(100%, 50%) rotate(30deg);
        }
        100%{
            box-shadow: 0 0 20px lightgray;
            transform: translate(80%, 50%) rotate(0deg);
        }

    }


    .hover-sign.acttive{
       display: none;
    }


    .skills-section{
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
    }

    .skills-box{
        width: 100%;
        height: 90vh;
        position: relative;
        display: flex;
        align-items: start;
        justify-content: center;
    }

    .skills-image{
        width: 70%;
        mix-blend-mode: lighten;
        opacity: 0.7;

    }



    .designer{
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        top: 25%;
        left: 5%;
        max-width: 300px;
    }

    .coder{
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        top: 25%;
        right: 5%;
        max-width: 300px;
    }

    .skills-box h1{
        font-size: 50px;
        display: flex;
        align-items: center;
    }

    .skills-box p{
        line-height: 23px;
    }

    .slider {
        position: absolute;
        top: 112%;
        bottom: 5%;
        right: 20%;
        width: 60%;
        height: var(--height);
        overflow: hidden;
        mask-image: linear-gradient(
            to right,
            transparent,
            #000000f8 10% 90%,
            transparent
        );
        mix-blend-mode: difference;
        opacity: 0.8;
        transition: width 0.4s ease, right 0.4s ease, top 0.4s ease;
        /* Enhanced performance and accessibility */
        will-change: transform, width, opacity;
        backface-visibility: hidden;
        transform: translateZ(0); /* Hardware acceleration */
    }

    .slider.list{
        display: flex;
        width: 100%;
        min-width: calc(var(--width)* var(--quantity));
        position: relative;
    }



    .slider .item {
        width: var(--width);
        height: var(--height);
        position: absolute;
        left: 100%;
        animation: autoRun 10s linear infinite;
        animation-delay: calc((10s / var(--quantity)) * (var(--position) - 1) - 10s);
      }




    .slider.list.item{
        width: var(--width);
        height: var(--height);
        position: absolute;
        left: 100%;
        animation: autoRun 10s linear infinite;
        transition: filter 05s;
        animation-delay: calc( (10s / var(--quantity)) * (var(--position)  -1) - 10s)!important;
    }

    .slider .list .item img{
        width: 70%;
        gap: 3px;
    }


    @keyframes autoRun {
        from{
            left: 100%;
        }to{
            left: calc(var(--width) * -1);
        }
    }


    .slider:hover .item{
        animation-play-state: paused!important;
        filter: grayscale(1);
    }


    .slider .item:hover{
        filter: grayscale(0);
    }


/* ========================================
   CONTACT SECTION
   ======================================== */

/* Contact Section Container - Main contact page layout */
    .contact-section{
        height: 100vh;
        display: flex;
        justify-content: center;
        gap: 10%;
        align-items: center;
        position: relative;
        flex-direction: row;
        margin-top: 0;
        margin-bottom: 0;
    }

/* Contact Section Title - Main heading for contact area */
    .contact-section h1{
        position: absolute;
        top: 12%;
        left: 40%;
    }

/* Social Contact Box - Container for social media contact info */
    .social-box{
        display: flex;
        flex-direction: column;
        gap: 25px;
    }



    .social-box i{
        color: #727fde;
        font-size: 30px;
        margin-right: 10px;
    }


    .social-icons {
        display: flex;
        gap: 18px;
        justify-content: center;
        align-items: center;
        position: relative;
        z-index: 10;
        pointer-events: auto;
    }

    .social-icons a {
        color: #727fde;
        font-size: 2rem;
        transition: all 0.3s ease;
        padding: 12px;
        display: inline-block;
        border-radius: 50%;
        background: rgba(114, 127, 222, 0.1);
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .social-icons a::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(114, 127, 222, 0.2);
        border-radius: 50%;
        transition: all 0.4s ease;
        transform: translate(-50%, -50%);
        z-index: -1;
    }

    .social-icons a:hover {
        color: #fff;
        background: #727fde;
        transform: translateY(-5px) scale(1.1);
        box-shadow: 0 10px 25px rgba(114, 127, 222, 0.4);
        border-color: #727fde;
    }

    .social-icons a:hover::before {
        width: 100%;
        height: 100%;
    }

    .social-icons a:active {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 5px 15px rgba(114, 127, 222, 0.3);
    }

    /* Individual platform colors on hover */
    .social-icons a[href*="facebook"]:hover {
        background: #1877f2;
        box-shadow: 0 10px 25px rgba(24, 119, 242, 0.4);
    }

    .social-icons a[href*="instagram"]:hover {
        background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
        box-shadow: 0 10px 25px rgba(225, 48, 108, 0.4);
    }

    .social-icons a[href*="linkedin"]:hover {
        background: #0077b5;
        box-shadow: 0 10px 25px rgba(0, 119, 181, 0.4);
    }

    .social-icons a[href*="x.com"]:hover,
    .social-icons a[href*="twitter"]:hover {
        background: #000000;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
    }

/* Contact Form Paragraph Text - Contact form description text */
    .contact-box p{
        max-width: 600px;
        margin-top: 30px;
        margin-bottom: 5px;
        height: 25px;
        border: none;
        outline: none;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
    }

/* Contact Form Submit Button - Send message button styling */
    .contact-box button{
        margin-top: 30px;
        color: white;
        padding: 10px 15px;
        border-radius: 15px;
        border: 1px solid #727fdeb4;
        background-color: #2200493d;
        box-shadow:  0 0 5px #727fde86;
        cursor: pointer;
        transition: 0.3s;
    }

/* Contact Form Container - Main contact form wrapper with glassmorphism */
    .contact-box {
        background: rgba(34, 0, 73, 0.15);
        border-radius: 18px;
        box-shadow: 0 4px 24px #727fde33;
        padding: 25px;
        transition: box-shadow 0.3s;
        max-width: 420px;
        width: 420px;
        height: 420px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

/* Contact Form Labels - Input field labels styling */
    .contact-box label{
        display: block;
        color: #727fde;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 5px;
        margin-top: 12px;
    }

/* First Contact Form Label - Removes top margin from first label */
    .contact-box label:first-of-type{
        margin-top: 0;
    }

/* Contact Form Input Fields - Text inputs and textarea styling */
    .contact-box input,
    .contact-box textarea{
        width: 100%;
        padding: 12px 15px;
        margin-bottom: 12px;
        border: 1px solid #727fde77;
        border-radius: 10px;
        background-color: #2200493d;
        color: white;
        font-size: 14px;
        outline: none;
        transition: 0.3s;
        box-sizing: border-box;
    }

/* Contact Form Input Focus - Focus state styling for form fields */
    .contact-box input:focus,
    .contact-box textarea:focus{
        border-color: #727fde;
        box-shadow: 0 0 10px #727fde44;
    }

    /* Form Status Styling */
    .form-status {
        padding: 12px 16px;
        border-radius: 8px;
        margin-bottom: 20px;
        font-weight: 600;
        text-align: center;
        display: none;
        transition: all 0.3s ease;
    }

    .form-status.success {
        background: rgba(34, 197, 94, 0.15);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.3);
    }

    .form-status.error {
        background: rgba(239, 68, 68, 0.15);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.3);
    }

    .hidden {
        display: none !important;
    }



    .contact-box button:hover{
        box-shadow: 0 0 15px #727fde6f;
        opacity: 0.7;
    }



    footer{
        display: flex;
        position: absolute;
        bottom: 0;
        right: 0;
        left: 0;
        align-items: center;
        justify-content: center;
        height: 70px;
        backdrop-filter: blur(8px);
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.356);
        z-index: 999;
    }


    footer h1{
        font-size: 25px;
    }


    .menu-icon{
        font-size: 35px;
        cursor: pointer;
        display: none;
    }

    .sidebar{
        position: fixed;
        top: 70px; /* Position below the navbar */
        right: 0;
        width: 300px;
        height: calc(100vh - 70px); /* Full height minus navbar */
        background-color: rgba(136, 133, 133, 0.911);
        backdrop-filter: blur(10px);
        z-index: 999;
        opacity: 0.8; /* Single opacity value */
        overflow: hidden;
        transform: translateX(100%);
        transition: transform 0.3s ease, opacity 0.3s ease, width 0.4s ease, right 0.4s ease, top 0.4s ease;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        padding-top: 30px; /* Reduced padding since it's below navbar */
        border-top: 2px solid rgba(135, 135, 231, 0.3);
        /* Optional visual effects */
        mask-image: linear-gradient(
            to right,
            transparent,
            #000000f1 10% 90%,
            transparent
        );
        mix-blend-mode: difference;
    }

    .sidebar.open-sidebar {
        animation: openSidebarAnimation 0.5s forwards;
    }


    .sidebar.close-sidebar {
        animation: closeSidebarAnimation 0.5s forwards;
    }



    .sidebar ul{
        padding-left: 20px;
    }


    .sidebar ul li{
        list-style: none;
        margin-bottom: 30px;
    }


    .sidebar ul li a{
        text-decoration: none;
        color: lightgray;
        font-size: 30px;
        font-weight: 700;
        text-shadow: 0 0 15px rgb(128, 128, 128, 0.256);
    }

    .sidebar .social-icon{
        padding-left: 20px;
        margin-top: 60px;
        text-wrap: nowrap;
    }

    .sidebar .social-icon a{
        color: white;
        font-size: 35px;
        padding: 5px 5px;
        cursor: pointer;
    }


    .sidebar.open-sidebar {
        transform: translateX(0);
        opacity: 0.6;
      }

      @keyframes openSidebarAnimation {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }



.sidebar.close-sidebar{
    animation:closeSidebarAnimation 1.5s forwards;
}

    @keyframes closeSidebarAnimation{
        from{
            right: 0;
            opacity: 1;
        }
        to{
            right: -300px;
            opacity: 0;
        }
    }





    .autoBlur {
        animation: autoBlurAnimation 1.2s ease both;
      }

      @supports (animation-timeline: view()) {
        .autoBlur {
          animation: autoBlurAnimation linear both;
          animation-timeline: view();
          animation-duration: auto;
        }
      }



      .autoDisplay {
        animation: autoDisplayAnimation both;
        animation-timeline: view();
      }


      @keyframes autoDisplayAnimation {
        from{
            filter: blur(10px);
            transform: translateY(-200px) scale(0);
            opacity: 0.2;
        }
        50%{
            opacity: 1;
            filter: blur(0);
            transform: translateX(0) scale(1);
        }
          }

      .fadeInRight{
        animation: fadeInAnimation both;
        animation-timeline: view();
      }

      @keyframes fadeInAnimation {
        0%{
            opacity: 0;
            transform: translateX(-500px) scale(0.2);
            filter: blur(20px);
        }
        35%, 65%{
            opacity: 1;
            transform: translateX(0px) scale(1);
            filter: blur(0);
        }
        100%{
            filter: blur(20px);
        }
      }



/* About Section Styles */
.about-section {
  padding: 60px 20px;
  text-align: center;
  background: transparent;
}

.about-section .section-title {
  font-size: 2.5rem;
  margin-bottom: 30px;
  color: #727fde;
  letter-spacing: 1px;
}

.about-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  max-width: 700px;
  margin: 0 auto;
  background: rgba(34, 0, 73, 0.10);
  border-radius: 18px;
  box-shadow: 0 4px 24px #727fde22;
  padding: 32px 20px;
}

.about-photo {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 50%;
  margin-bottom: 16px;
  border: 3px solid #727fde;
  box-shadow: 0 2px 12px #727fde33;
}

.about-content p {
  font-size: 1.1rem;
  color: #eaeaea;
  margin-bottom: 10px;
  line-height: 1.6;
  text-align: left;
}

@media (max-width: 700px) {
  .about-content {
    padding: 20px 21px;
    gap: 16px;
  }
  .about-section .section-title {
    font-size: 2rem;
  }
  .about-content p {
    font-size: 1rem;
    text-align: center;
  }
}


@media (max-aspect-ratio: 16/9) {
    .back-videos{
        width: auto;
        height: 100%;
    }

}


@media (min-aspect-ratio: 16/9) {
    .back-videos{
        width: 100%;
        height: auto;

    }   }



/* Large Desktop and 4K Displays */
@media screen and (min-width: 1920px) {
    .container {
        max-width: 1800px;
        margin: 0 auto;
    }

    .hero-info h1 {
        font-size: 80px;
        line-height: 90px;
    }

    .hero-info p {
        font-size: 24px;
        line-height: 32px;
    }

    .card h1 {
        font-size: 32px;
    }

    .card p {
        font-size: 18px;
        line-height: 24px;
    }

    .downloads-section {
        width: 50%;
        max-width: 1400px;
    }
}

/* Desktop and Large Tablets */
@media screen and (min-width: 1200px) and (max-width: 1919px) {
    .hero-info h1 {
        font-size: 70px;
        line-height: 80px;
    }

    .downloads-section {
        width: 55%;
        max-width: 1200px;
    }
}

/* Standard Desktop */
@media screen and (min-width: 1001px) and (max-width: 1199px) {
    .hero-info h1 {
        font-size: 60px;
        line-height: 70px;
    }

    .downloads-section {
        width: 65%;
    }

    .sidebar {
        width: 280px;
    }
}

@media screen and (max-width: 1000px){
    .blackhole-box video{
        margin-top: -10%;
    }

    .hero-info-title h1{
        font-size: 13px;
        max-width: 300px;
        line-height: 15px;
    }


    .hero-info p{
        max-width: 300px;
    }

   .hero-info-box{
    right: 0;
   }

   .hero-video-box video{
    height: 260px;
   }


   .section-title{
    font-family: 30px;
   }

   .info-card{
    grid-template-columns: auto;
   }

   .card:nth-child(3){
    grid-column: span 2;
    height: 70vh;
   }

   .info-card .card h1{
    font-size: 20px;
    max-width: 90%;
    overflow-wrap: break-word;
    word-wrap: break-word;
   }

   .card:nth-child(3) h1{
    bottom: 25%;
   }

   .info-card .card p {
    max-width: 90%;
    overflow-wrap: break-word;
    word-wrap: break-word;
   }

   .card video{
    height: 56%;
    margin-top: 5%;
   }

   .my-pojects{
    margin-bottom: 300px;
   }

   .pojects-vidbox video{
    width: 250px;
    margin-left: -100px;
   }

   .pojects-info{
    padding-left: 0;
    margin-left: -50px;
   }

   .poject_info h1{
    font-size: 20px;
    max-width: 200px;
    text-wrap: wrap;
   }

   .poject_info p{
    font-size: 10px;
    text-wrap: wrap;
    max-width: 200px;
    min-width: 0;
   }

   .pojects-info h1 {
        font-size: 20px;
        max-width: 300px;
    }

    .pojects-info p {
        font-size: 14px;
        max-width: 250px;
    }

   footer h1{
    font-size: 20px;
   }
}

/* iPad Pro and Large Tablets */
@media screen and (min-width: 768px) and (max-width: 1024px) {
    .hero-info h1 {
        font-size: 50px;
        line-height: 60px;
    }

    .hero-info p {
        font-size: 18px;
        line-height: 24px;
    }

    .card h1 {
        font-size: 24px;
    }

    .card p {
        font-size: 14px;
        line-height: 18px;
    }

    .downloads-section {
        width: 75%;
    }

    .sidebar {
        width: 320px;
    }

    .pojects-info h1 {
        font-size: 22px;
    }

    .pojects-info p {
        font-size: 16px;
        line-height: 22px;
    }
}

/* iPad and Standard Tablets */
@media screen and (min-width: 701px) and (max-width: 767px) {
    .hero-info h1 {
        font-size: 45px;
        line-height: 55px;
    }

    .downloads-section {
        width: 80%;
    }

    .sidebar {
        width: 300px;
    }
}

@media screen and (max-width: 900px) {
    .slider {
        width: 90%;
        right: 5%;
    }
}

@media screen and (max-width: 700px) {
    header{
        position: fixed;
        height: 65px;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background-color: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(10px);
    }

    /* Improved card text responsiveness */
    .card h1 {
        font-size: 22px;
        max-width: 90%;
        line-height: 1.3;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }

    .card p {
        font-size: 12px;
        line-height: 16px;
        max-width: 90%;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }

    header ul{
        display: none;
    }

    header .box-icon{
        display: none;
    }

    header h1{
        font-size: 25px;
    }

    .blackhole-box video{
        margin-top: -15%;
    }

    .hero{
        flex-direction: column;
    }

    .autoBlur{
        animation: none;
    }

    .hero-info{
        bottom: 5%;
    }
    .scroll-down{
        bottom: 5%;
    }

    .hero-info h1{
        line-height: 50px;
    }

    .hero-video-box{
        height: 700px;
        top: 10%;
        right: 35%;
    }

    .card video{
        width: 200%;
    }

    .my-pojects{
        margin-bottom: 600px;
    }

    .poject-card{
        flex-direction: column;
        margin-left: 20%;
        gap: 30px;
    }

    .poject_info{
        width: 85%;
    }

    .poject_info h1{
        text-wrap: nowrap;
    }

    .poject_info p{
        max-width: 300px;
    }

    .designer{
        top: 15%;
        left: 18%;
    }

    .coder{
     top: 62%;
     left: 18%;
    }

    .skills-box h1{
        margin-bottom: 0;
        margin-top: 80px;
    }

    .slider{
        bottom: 0;
    }

    .slider img{
        width: 60%;
    }

    .contact-section{
        flex-direction: column;
        margin-top: 100px;
        margin-bottom: 60px;
        height: auto;
        gap: 40px;
    }

    .contact-section  .section-title{
        top: -40px;
        left: 25%;
    }

    .contact-section h1 {
        position: static;
        margin-bottom: 30px;
        left: auto;
        top: auto;
        text-align: center;
    }

    footer h1{
        font-size: 20px;
    }

    .pojects-vidbox{
        min-width: 200px;
    }

    .menu-icon{
        display: inline;
        color: white;
        z-index: 1001;
        position: relative;
        padding: 10px;
        border-radius: 8px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.1);
    }

    .menu-icon:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
    }

    /* Mobile Sidebar Optimizations */
    .sidebar {
        top: 65px !important; /* Position below mobile navbar */
        height: calc(100vh - 65px) !important; /* Adjust height for mobile navbar */
        width: 85% !important;
        background: rgba(63, 62, 62, 0.9) !important;
        backdrop-filter: blur(15px) !important;
        border-left: 2px solid rgba(114, 127, 222, 0.3);
        border-top: 2px solid rgba(114, 127, 222, 0.3);
        padding-top: 20px !important;
    }

    .sidebar.open-sidebar {
        width: 85% !important;
        opacity: 1 !important;
        transform: translateX(0) !important;
        animation: none !important;
    }

    .sidebar ul {
        padding: 20px;
        margin-top: 40px;
    }

    .sidebar ul li {
        margin-bottom: 25px;
        padding: 15px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .sidebar ul li a {
        font-size: 24px;
        display: block;
        padding: 10px 0;
        transition: all 0.3s ease;
        border-radius: 8px;
    }

    .sidebar ul li a:hover {
        color: #727fde;
        background: rgba(114, 127, 222, 0.1);
        padding-left: 15px;
        transform: translateX(10px);
    }

    .sidebar .social-icon {
        padding: 20px;
        margin-top: 40px;
        display: flex;
        justify-content: center;
        gap: 20px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .sidebar .social-icon a {
        font-size: 28px;
        padding: 12px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
    }

    .sidebar .social-icon a:hover {
        background: rgba(114, 127, 222, 0.3);
        transform: scale(1.1);
        color: #727fde;
    }

    .pojects-info h1 {
        font-size: 18px;
        max-width: 200px;
    }

    .pojects-info p {
        font-size: 13px;
        max-width: 90%;
        line-height: 1.5;
        overflow-wrap: break-word;
        word-wrap: break-word;
        margin-bottom: 30px;
    }

    .contact-box {
        width: 90%;
        padding: 20px 10px;
        margin: 0 auto;
    }
    .contact-box input {
        width: 100%;
        font-size: 14px;
    }
    .contact-box button {
        width: 100%;
        font-size: 15px;
    }
}

/* Enhanced Slider Responsiveness */
@media screen and (max-width: 900px) {
    .slider {
        width: 90%;
        right: 5%;
        top: 115%;
        opacity: 0.9;
    }
}

@media screen and (max-width: 700px) {
    .slider {
        width: 95%;
        right: 2.5%;
        top: 118%;
        bottom: 2%;
        opacity: 1;
        /* Simplify mask for better mobile performance */
        mask-image: linear-gradient(
            to right,
            transparent,
            #000000 15% 85%,
            transparent
        );
    }

    .slider img {
        width: 60%;
        height: auto;
        object-fit: contain;
    }
}

@media screen and (max-width: 600px) {
    .slider {
        width: 100%;
        right: 0;
        top: 120%;
        bottom: 0;
        /* Further optimize for small screens */
        mix-blend-mode: normal;
        opacity: 0.85;
    }

    .slider img {
        width: 55%;
        max-height: 80px;
    }
}

@media screen and (max-width: 500px) {
    .pojects-info h1 {
        font-size: 16px;
        max-width: 90%;
        line-height: 1.3;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .pojects-info p {
        font-size: 12px;
        max-width: 90%;
        line-height: 1.4;
        margin-bottom: 25px;
    }

    .poject-card {
        margin-left: 10%;
        width: 90%;
    }

    /* Mobile Sidebar for small screens */
    .sidebar {
        top: 65px !important;
        height: calc(100vh - 65px) !important;
        width: 90% !important;
        padding-top: 15px !important;
    }

    .sidebar ul li a {
        font-size: 22px;
    }

    .sidebar .social-icon a {
        font-size: 24px;
        width: 45px;
        height: 45px;
    }
}

@media screen and (max-width: 430px) {
    .hero-info-title {
        margin-top: 80px;
        text-align: center;
    }

    .contact-box {
        padding: 10px 2px;
    }
    .contact-box p {
        font-size: 13px;
        max-width: 95vw;
    }

    /* Very small screen sidebar optimizations */
    .sidebar {
        top: 65px !important;
        height: calc(100vh - 65px) !important;
        width: 95% !important;
        padding-top: 15px !important;
    }

    .sidebar ul {
        padding: 15px;
        margin-top: 20px;
    }

    .sidebar ul li {
        margin-bottom: 20px;
        padding: 12px 0;
    }

    .sidebar ul li a {
        font-size: 20px;
        padding: 8px 0;
    }

    .sidebar .social-icon {
        padding: 15px;
        margin-top: 30px;
        gap: 15px;
    }

    .sidebar .social-icon a {
        font-size: 22px;
        width: 40px;
        height: 40px;
    }

    .close-icon {
        top: 15px;
        right: 15px;
        width: 45px;
        height: 45px;
    }

    .close-icon i {
        font-size: 22px;
    }

    .menu-icon {
        font-size: 30px;
        padding: 8px;
    }
}

.close-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1002;
}

.close-icon:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.close-icon i {
    font-size: 24px;
    color: white;
    cursor: pointer;
}

a:focus-visible, button:focus-visible {
    outline: 2px solid #6070fd;
    outline-offset: 2px;
}

/* AI Chat Board Styles */
#ai-chat-board {
  position: fixed;
  bottom: 80px;
  right: 30px;
  width: 320px;
  max-width: 95vw;
  background: #181c24;
  color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.25);
  display: none;
  flex-direction: column;
  z-index: 1000;
  overflow: hidden;
}

#ai-chat-header {
  background: #23272f;
  padding: 12px 16px;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* AI Header Content - Contains robot icon and title */
.ai-header-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* AI Robot Icon - Styling for the robot icon in header */
.ai-header-content i {
  color: #00bcd4;
  font-size: 18px;
  animation: robotPulse 2s ease-in-out infinite;
}

/* Robot Pulse Animation - Subtle pulsing effect for the robot icon */
@keyframes robotPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

#ai-chat-close {
  cursor: pointer;
  font-size: 20px;
}

#ai-chat-messages {
  padding: 16px;
  height: 220px;
  overflow-y: auto;
  background: #20232a;
  font-size: 15px;
}

#ai-chat-form {
  display: flex;
  border-top: 1px solid #23272f;
  background: #181c24;
}

#ai-chat-input {
  flex: 1;
  border: none;
  padding: 12px;
  background: #23272f;
  color: #fff;
  font-size: 15px;
  border-radius: 0 0 0 12px;
  outline: none;
}

#ai-chat-form button {
  background: #00bcd4;
  color: #fff;
  border: none;
  padding: 0 18px;
  font-size: 15px;
  cursor: pointer;
  border-radius: 0 0 12px 0;
  transition: background 0.2s;
}

#ai-chat-form button:hover {
  background: #0097a7;
}

#ai-chat-toggle {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background: #00bcd4;
  color: #fff;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(0,0,0,0.18);
  z-index: 1001;
  transition: all 0.3s ease;
}

/* AI Toggle Robot Icon - Styling for the robot icon in toggle button */
#ai-chat-toggle i {
  animation: robotBounce 3s ease-in-out infinite;
}

/* Robot Bounce Animation - Bouncing effect for the toggle robot icon */
@keyframes robotBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
  60% {
    transform: translateY(-4px);
  }
}

/* AI Toggle Hover Effect - Enhanced hover state */
#ai-chat-toggle:hover {
  background: #0097a7;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0,0,0,0.25);
}

.gemini-btn {
  position: fixed;
  bottom: 30px;
  left: 30px;
  background: #fff;
  color: #00bcd4;
  padding: 10px 18px;
  border-radius: 24px;
  text-decoration: none;
  font-weight: bold;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  transition: background 0.2s, color 0.2s;
}

.gemini-btn:hover {
  background: #00bcd4;
  color: #fff;
}

/* Mobile Chat Styles */
@media (max-width: 768px) {
  #ai-chat-board {
    width: 90%;
    bottom: 100px;
    right: 5%;
    max-height: 60vh;
    border-radius: 16px;
  }

  #ai-chat-toggle {
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    font-size: 24px;
    z-index: 1002;
    /* Always visible on mobile */
    display: flex !important;
  }

  /* Add a visual indicator when chat is open */
  #ai-chat-toggle.active {
    background-color: #0097a7;
    transform: rotate(45deg);
  }
}

@media screen and (max-width: 375px) {
  .download-card {
    overflow-wrap: break-word;
    word-wrap: break-word;
    padding: 10px;
  }
  .download-info h2 {
    overflow-wrap: break-word;
    word-wrap: break-word;
    font-size: 16px;
  }
  .download-meta {
    font-size: 10px;
  }

  /* Fix for card text on very small screens */
  .card h1 {
    font-size: 18px;
    max-width: 90%;
    bottom: 45%;
    line-height: 1.2;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }

  .card p {
    font-size: 12px;
    line-height: 16px;
    max-width: 90%;
    bottom: 2%;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }

  /* Fix for project section on very small screens */
  .pojects-info h1 {
    font-size: 15px;
    max-width: 95%;
    line-height: 1.3;
  }

  .pojects-info p {
    font-size: 11px;
    max-width: 95%;
    line-height: 1.4;
    margin-bottom: 20px;
  }

  .poject-card {
    margin-left: 5%;
    width: 95%;
  }

  .pojects-vidbox {
    min-width: 150px;
  }

  /* Slider optimizations for very small screens */
  .slider {
    width: 100%;
    right: 0;
    top: 125%;
    bottom: 0;
    opacity: 0.8;
    /* Simplified mask for performance */
    mask-image: linear-gradient(
      to right,
      transparent,
      #000000 20% 80%,
      transparent
    );
    /* Disable blend mode for better performance */
    mix-blend-mode: normal;
  }

  .slider img {
    width: 45%;
    max-height: 60px;
    object-fit: contain;
  }
}

/* Add vertical gap between .list items inside .downloads-section */
.downloads-section .list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

@media (max-width: 700px) {
  .downloads-section .list {
    gap: 20px;
  }
}

/* iPhone 14 Pro Max and Similar Large Phones */
@media screen and (max-width: 430px) and (min-width: 414px) and (min-height: 896px) {
  .hero-info h1 {
    font-size: 46px;
    line-height: 52px;
    max-width: 90%;
  }

  .hero-info p {
    font-size: 17px;
    max-width: 90%;
    line-height: 23px;
  }

  .card h1 {
    font-size: 19px;
    max-width: 92%;
    line-height: 1.3;
  }

  .card p {
    font-size: 13px;
    line-height: 17px;
    max-width: 92%;
  }

  .downloads-section {
    width: 94%;
    padding: 25px 15px;
  }

  .sidebar {
    width: 92% !important;
  }
}

/* iPhone 13/14 Standard and Similar */
@media screen and (max-width: 414px) and (min-width: 390px) and (min-height: 844px) {
  .hero-info h1 {
    font-size: 44px;
    line-height: 50px;
    max-width: 90%;
  }

  .hero-info p {
    font-size: 16px;
    max-width: 90%;
    line-height: 22px;
  }

  .downloads-section {
    width: 95%;
    padding: 22px 12px;
  }
}

/* iPhone 12 Mini and Similar Compact Phones */
@media screen and (max-width: 375px) and (min-width: 360px) and (min-height: 812px) {
  .hero-info h1 {
    font-size: 40px;
    line-height: 46px;
    max-width: 95%;
  }

  .hero-info p {
    font-size: 15px;
    max-width: 95%;
    line-height: 21px;
  }

  .card h1 {
    font-size: 17px;
    max-width: 95%;
  }

  .card p {
    font-size: 12px;
    line-height: 16px;
    max-width: 95%;
  }

  .downloads-section {
    width: 98%;
    padding: 20px 10px;
  }

  .sidebar {
    width: 98% !important;
  }
}

/* Samsung Galaxy S23 Ultra Specific Optimizations */
@media screen and (max-width: 430px) and (min-width: 400px) and (min-height: 850px) {
  /* Galaxy S23 Ultra viewport optimization */
  .hero-info h1 {
    font-size: 48px;
    line-height: 55px;
    max-width: 90%;
  }

  .hero-info p {
    font-size: 18px;
    max-width: 90%;
    line-height: 24px;
  }

  .hero-info-title {
    width: 280px;
    font-size: 14px;
  }

  /* Card optimizations for Galaxy S23 Ultra */
  .card h1 {
    font-size: 20px;
    max-width: 92%;
    line-height: 1.3;
  }

  .card p {
    font-size: 13px;
    line-height: 18px;
    max-width: 92%;
  }

  /* Project section optimizations */
  .pojects-info h1 {
    font-size: 18px;
    max-width: 92%;
    line-height: 1.4;
  }

  .pojects-info p {
    font-size: 13px;
    max-width: 92%;
    line-height: 1.5;
    margin-bottom: 25px;
  }

  /* Downloads section for Galaxy S23 Ultra */
  .downloads-section {
    width: 96%;
    padding: 25px 15px;
  }

  .download-card {
    padding: 18px;
    max-width: 95%;
  }

  .download-info h2 {
    font-size: 18px;
  }

  .download-info p {
    font-size: 14px;
  }

  /* Navigation optimizations */
  header {
    height: 70px;
    padding: 0 25px;
  }

  .left h1 {
    font-size: 22px;
  }

  /* Contact section optimizations */
  .contact-box {
    width: 92%;
    padding: 25px 15px;
  }

  .contact-box input,
  .contact-box textarea {
    font-size: 16px;
    padding: 12px;
  }

  /* Skills section optimizations */
  .skills-box h1 {
    font-size: 42px;
  }

  .skills-box p {
    font-size: 14px;
    line-height: 20px;
  }

  /* Slider optimizations for Galaxy S23 Ultra */
  .slider {
    width: 98%;
    right: 1%;
    top: 125%;
    bottom: 1%;
    opacity: 0.9;
    /* Optimize mask for high-resolution display */
    mask-image: linear-gradient(
      to right,
      transparent,
      #000000 12% 88%,
      transparent
    );
  }

  .slider img {
    width: 50%;
    max-height: 70px;
    object-fit: contain;
  }

  /* Footer optimizations */
  footer h1 {
    font-size: 18px;
    padding: 0 15px;
    text-align: center;
  }

  /* AI Chat optimizations for Galaxy S23 Ultra */
  #ai-chat-board {
    width: 92%;
    bottom: 90px;
    right: 4%;
    max-height: 65vh;
  }

  #ai-chat-toggle {
    bottom: 25px;
    right: 25px;
    width: 55px;
    height: 55px;
    font-size: 26px;
  }
}

/* Ultra-wide screen optimization (Galaxy S23 Ultra landscape) */
@media screen and (min-width: 850px) and (max-width: 950px) and (max-height: 430px) {
  .hero {
    flex-direction: row;
    height: 100vh;
  }

  .hero-info {
    position: static;
    width: 50%;
    padding: 20px;
  }

  .hero-video-box {
    position: static;
    width: 50%;
    height: auto;
  }

  .hero-video-box video {
    height: 300px;
    width: 100%;
  }

  .my-pojects {
    margin-bottom: 200px;
  }

  .poject-card {
    flex-direction: row;
    margin-left: 5%;
    gap: 20px;
    width: 90%;
  }

  .contact-section {
    flex-direction: row;
    height: 80vh;
    gap: 5%;
  }
}

/* Additional Device-Specific CSS Classes */
/* These classes are added by JavaScript based on device detection */

.iphone-14-pro-max .hero-info h1 {
  font-size: 46px !important;
  line-height: 52px !important;
}

.iphone-13-14 .hero-info h1 {
  font-size: 44px !important;
  line-height: 50px !important;
}

.iphone-mini .hero-info h1 {
  font-size: 40px !important;
  line-height: 46px !important;
}

.galaxy-s23-ultra .hero-info h1 {
  font-size: 48px !important;
  line-height: 55px !important;
}

.ipad-pro .downloads-section {
  width: 70% !important;
}

.large-tablet .sidebar {
  width: 320px !important;
}

.high-dpi img,
.high-dpi video {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.landscape.mobile .hero {
  flex-direction: row !important;
  height: 100vh !important;
}

.landscape.mobile .hero-info {
  position: static !important;
  width: 50% !important;
  padding: 20px !important;
}

.landscape.mobile .hero-video-box {
  position: static !important;
  width: 50% !important;
}

/* Universal Accessibility and Performance Improvements */
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-touch-callout: none;
}

button, a, input, textarea, select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Smooth scrolling for all devices */
html {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Better text rendering */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Prevent zoom on input focus for mobile */
@media screen and (max-width: 768px) {
  input, textarea, select {
    font-size: 16px !important;
  }
}

/* Print styles */
@media print {
  .sidebar, .menu-icon, #ai-chat-board, #ai-chat-toggle, .gemini-btn {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}