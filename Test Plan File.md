# Test Plan: Portfolio Website

## 1. Functional Testing

### 1.1 Navigation
- Verify all navigation links (Home, About, Downloads, Contact) scroll or navigate to the correct section.
- Sidebar menu opens and closes correctly on click (desktop and mobile).
- "Browse Downloads" button in hero section scrolls to the Downloads section.

### 1.2 Video Functionality
- All videos play on hover (desktop).
- All videos play/pause on tap (mobile/tablet).
- Videos have `muted` and `playsinline` attributes for mobile compatibility.

### 1.3 Contact Form
- All input fields accept text.
- "Send Message" button is clickable.
- Placeholder text is visible in all fields.

### 1.4 Social Links
- All social media icons are clickable.
- Links open the correct social media pages (if URLs are set).

### 1.5 Downloads Center
- All download buttons are clickable and show "Downloaded" confirmation.
- Audio players in music downloads section function correctly (play, pause, volume controls).
- Download cards display all information correctly (title, description, metadata).
- Category titles and icons display correctly.

---

## 2. Responsive Design Testing

### 2.1 Desktop (≥1200px)
- Layout is wide, all sections are side-by-side as designed.
- No horizontal scroll.
- Downloads section is centered with 2-column grid layout.

### 2.2 Tablet/iPad (600px–1200px)
- Layout stacks vertically where appropriate.
- Font sizes and paddings adjust for readability.
- Videos and images scale to fit the screen.
- Downloads section adjusts to single column layout.

### 2.3 Mobile (≤600px)
- All sections stack vertically.
- Navigation collapses to sidebar/menu icon.
- Buttons and links are easy to tap.
- No horizontal scroll.
- Download cards stack vertically with centered content.
- Download card icons position above content.

### 2.4 Very Small Mobile (≤375px)
- Text wraps properly in download cards with no overflow.
- Download card padding reduces to 10px.
- Font sizes adjust (headings to 16px, metadata to 10px).
- Download metadata stacks vertically.
- No horizontal scroll on any content.

---

## 3. Animation & Effects

- `.autoBlur` and `.fadeInRight` animations trigger on scroll (desktop and mobile, if enabled).
- Hover effects on buttons and icons work as expected.
- Download cards animate on hover (slight elevation effect).
- Download buttons animate on hover and click (scale effect).
- "Downloaded" confirmation appears when download buttons are clicked.

---

## 4. Accessibility

- All interactive elements (buttons, links) are keyboard accessible.
- Focus outlines are visible on tab navigation.
- Sufficient color contrast for text and backgrounds.
- Images and videos have `alt` text or are decorative.
- Download buttons are accessible via keyboard.
- Audio players have accessible controls.
- Download card content is readable at all screen sizes.

---

## 5. Performance

- Page loads within 3 seconds on broadband.
- No broken images or videos.
- No 404 errors in the console.

---

## 6. Cross-Browser Testing

- Test on Chrome, Firefox, Edge, Safari (latest versions).
- Test on Android and iOS browsers.

---

## 7. Other

- Footer displays correctly on all devices.
- No console errors or warnings.
- Downloads section is properly centered in the layout.
- Download cards maintain consistent spacing and alignment.
- All download categories (Software, Windows OS, Linux OS, Music) display correctly.

## 8. Downloads Center Specific Tests

### 8.1 Layout Tests
- Downloads section is centered on all screen sizes.
- Download cards are centered within their grid cells.
- Category titles are properly centered and styled.
- Download grid maintains proper spacing between cards.

### 8.2 Content Tests
- All download cards display correct information.
- Icons match their respective categories (software, Windows, Linux, music).
- Metadata (version, date, size) is properly formatted.
- Audio players in music section are properly sized and functional.

### 8.3 Mobile Responsiveness Tests
- Test text wrapping on very small screens (375px width).
- Verify download metadata stacks vertically on small screens.
- Check that download buttons remain properly sized and clickable.
- Ensure no horizontal scrolling occurs at any screen width.

---

**Test each item above on all major device sizes and browsers. Document any issues and retest after fixes.**