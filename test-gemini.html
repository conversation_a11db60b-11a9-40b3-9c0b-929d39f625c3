<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gemini API Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    h1 {
      color: #333;
    }
    .container {
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    textarea {
      width: 100%;
      height: 100px;
      margin-bottom: 10px;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #45a049;
    }
    #response {
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #f9f9f9;
      white-space: pre-wrap;
    }
    .error {
      color: red;
    }
    .success {
      color: green;
    }
    #apiKeyInput {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Gemini API Test</h1>

    <div>
      <label for="apiKeyInput">Your Gemini API Key:</label>
      <input type="text" id="apiKeyInput" placeholder="Enter your API key here" value="AIzaSyBIIg6QmcuC3qrI_YavYETZQMfYs3gFsvc">
    </div>

    <div>
      <label for="prompt">Enter your prompt:</label>
      <textarea id="prompt" placeholder="Type your message here...">Hello, can you tell me about yourself?</textarea>

      <div style="margin-top: 10px; margin-bottom: 15px;">
        <label>Example prompts:</label>
        <div style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px;">
          <button class="example-prompt" style="font-size: 12px; padding: 5px 10px; background: #673AB7; color: white; border: none; border-radius: 4px; cursor: pointer;">Explain how AI works</button>
          <button class="example-prompt" style="font-size: 12px; padding: 5px 10px; background: #673AB7; color: white; border: none; border-radius: 4px; cursor: pointer;">Write a short poem about technology</button>
          <button class="example-prompt" style="font-size: 12px; padding: 5px 10px; background: #673AB7; color: white; border: none; border-radius: 4px; cursor: pointer;">What are the benefits of responsive web design?</button>
          <button class="example-prompt" style="font-size: 12px; padding: 5px 10px; background: #673AB7; color: white; border: none; border-radius: 4px; cursor: pointer;">Give me 5 ideas for a portfolio website</button>
        </div>
      </div>
    </div>

    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
      <button id="sendButton">Send to Gemini</button>
      <button id="testKeyButton" style="background-color: #2196F3;">Test API Key Only</button>
    </div>

    <div>
      <h3>Response:</h3>
      <div id="response">Response will appear here...</div>
    </div>

    <div>
      <h3>Raw JSON Response:</h3>
      <pre id="rawResponse" style="overflow-x: auto; background: #f0f0f0; padding: 10px; font-size: 12px;"></pre>
    </div>
  </div>

  <script>
    // Handle example prompt buttons
    document.querySelectorAll('.example-prompt').forEach(button => {
      button.addEventListener('click', function() {
        document.getElementById('prompt').value = this.textContent;
      });
    });

    // Function to test just the API key
    document.getElementById('testKeyButton').addEventListener('click', async function() {
      const apiKey = document.getElementById('apiKeyInput').value.trim();
      const responseElement = document.getElementById('response');
      const rawResponseElement = document.getElementById('rawResponse');

      if (!apiKey) {
        responseElement.innerHTML = '<span class="error">Please enter your API key</span>';
        return;
      }

      responseElement.innerHTML = 'Testing API key...';
      rawResponseElement.textContent = '';

      try {
        // Simple test request to Gemini API
        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`, {
          method: 'GET'
        });

        const data = await response.json();
        rawResponseElement.textContent = JSON.stringify(data, null, 2);

        if (!response.ok) {
          responseElement.innerHTML = `<span class="error">API Key Error: ${data.error?.message || 'Unknown error'}</span>`;
          return;
        }

        // If we get here, the API key is valid
        responseElement.innerHTML = '<span class="success">✓ API Key is valid! The models available are listed in the Raw JSON Response below.</span>';

      } catch (error) {
        responseElement.innerHTML = `<span class="error">Error: ${error.message}</span>`;
        rawResponseElement.textContent = error.toString();
      }
    });

    // Main function to send a prompt to Gemini
    document.getElementById('sendButton').addEventListener('click', async function() {
      const apiKey = document.getElementById('apiKeyInput').value.trim();
      const prompt = document.getElementById('prompt').value.trim();
      const responseElement = document.getElementById('response');
      const rawResponseElement = document.getElementById('rawResponse');

      if (!apiKey) {
        responseElement.innerHTML = '<span class="error">Please enter your API key</span>';
        return;
      }

      if (!prompt) {
        responseElement.innerHTML = '<span class="error">Please enter a prompt</span>';
        return;
      }

      responseElement.innerHTML = 'Loading...';
      rawResponseElement.textContent = '';

      try {
        // Call Gemini API
        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            contents: [{
              parts: [{ text: prompt }]
            }]
          })
        });

        const data = await response.json();
        rawResponseElement.textContent = JSON.stringify(data, null, 2);

        if (!response.ok) {
          responseElement.innerHTML = `<span class="error">Error: ${data.error?.message || 'Unknown error'}</span>`;
          return;
        }

        if (data.candidates && data.candidates[0] && data.candidates[0].content &&
            data.candidates[0].content.parts && data.candidates[0].content.parts[0]) {
          responseElement.innerHTML = `<span class="success">${data.candidates[0].content.parts[0].text.replace(/\n/g, '<br>')}</span>`;
        } else {
          responseElement.innerHTML = '<span class="error">Unexpected response format</span>';
        }
      } catch (error) {
        responseElement.innerHTML = `<span class="error">Error: ${error.message}</span>`;
        rawResponseElement.textContent = error.toString();
      }
    });

    // Auto-test the API key when the page loads
    window.addEventListener('DOMContentLoaded', function() {
      // Wait a second before auto-testing to ensure everything is loaded
      setTimeout(function() {
        // Only auto-test if there's an API key already in the input
        if (document.getElementById('apiKeyInput').value.trim()) {
          document.getElementById('testKeyButton').click();
        }
      }, 1000);
    });
  </script>
</body>
</html>
